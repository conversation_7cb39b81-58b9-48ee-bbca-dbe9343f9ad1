#root ,body{
  max-width: 1280px; 
}

@keyframes gradientBorder {
  from {
    --angle: 0turn;
  }
  to {
    --angle: 1turn;
  }
}

@property --angle {
  syntax: "<angle>";
  inherits: true;
  initial-value: 0turn;
}

.animate-gradient-border {
    
  animation:  gradientBorder 3s linear infinite; /* Adjust animation speed */
  border: 2.5px solid transparent; /* Create a transparent border */
  background: linear-gradient(to right, #ffffff, #ffffff) padding-box, 
    conic-gradient(
      from var(--angle), 
      #f36fff 53.3deg,
      #268aff 90.46deg,
      #02fdfd 126.24deg,
      #1b6eea 178.82deg,
      #e67ce0 360deg
    )
    border-box; /* Apply the gradient to the border */
  background-clip: padding-box, border-box; /* Ensure content box isn't affected */
}

 /* app.css */

/* Keyframes for Left-to-Right Moving Text */
@keyframes move-ltr {
  0% {
    transform: translateX(-100%); /* Start completely off-screen to the left */
  }
  100% {
    transform: translateX(100vw); /* End completely off-screen to the right */
  }
}

/* Keyframes for Right-to-Left Moving Text */
@keyframes move-rtl {
  0% {
    transform: translateX(100vw); /* Start completely off-screen to the right */
  }
  100% {
    transform: translateX(-100%); /* End completely off-screen to the left */
  }
}

/* Keyframes for the Gradient Border Animation */
@keyframes gradient-border {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Apply the Animations */

/* For the Left-to-Right Moving Text */
.animate-move-ltr {
  animation: move-ltr linear infinite;
}

/* For the Right-to-Left Moving Text */
.animate-move-rtl {
  animation: move-rtl linear infinite;
}

/* For the Gradient Border */
.animate-gradient-border {
  /* This specifically targets the border div based on your component structure */
  animation: gradient-border 8s ease infinite;
}

/*
  Additional styles that you might have from Tailwind, but
  are good to be aware of if manually replicating:
*/
/* Example for the main container's shadow */
.shadow-\[inset_0_0_29px_3px_\#B5D8D7\] {
  box-shadow: inset 0 0 29px 3px #B5D8D7;
}

.shadow-xl\/30 {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 8px 10px -6px rgba(0, 0, 0, 0.08); /* Tailwind's shadow-xl */
    /* You'd need to adjust alpha directly if you want exact /30 transparency */
}


/* Glassy Effect (backdrop-blur and background opacity) */
.backdrop-blur-md {
  backdrop-filter: blur(12px); /* Adjust blur amount as needed */
}

.bg-white\/10 {
  background-color: rgba(255, 255, 255, 0.1); /* White with 10% opacity */
}

/* Default blur for the inner content (when not loading) */
.blur-\[2px\] {
  filter: blur(2px);
}

/* Specific blur for the inner content (when loading) */
.blur-\[3px\] { /* This was added for the loading state in your component */
  filter: blur(3px);
}

/* Transition for the inner content wrapper */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}


@keyframes popIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes popOut {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}

.preview-enter {
  animation: popIn 0.3s ease-out forwards;
}

.preview-exit {
  animation: popOut 0.3s ease-in forwards;
}

@keyframes gradient-shift {
  0% {
    background-position: 100% 50%; /* Start from the right */
  }
  50% {
    background-position: 0% 50%; /* Move to the left */
  }
  100% {
    background-position: 100% 50%; /* Go back to the right */
  }
}

.animate-gradient-shift {
  background-size: 200% 200%; /* Expand the gradient size */
  animation: gradient-shift 10s  cubic-bezier(0.23, 1, 0.320, 1) infinite; /* Animate the gradient over 5 seconds */
  -webkit-background-clip: text; /* Clip the gradient to the text */
  background-clip: text; /* Clip the gradient to the text */
}
