// Simple script to check if your backend server is running
// Run this with: node check-backend.js

const axios = require('axios');

const backendUrl = 'http://localhost:8000';

async function checkBackend() {
  console.log('🔍 Checking backend server at', backendUrl);
  
  try {
    // Try health endpoint
    console.log('📡 Trying health endpoint...');
    const healthResponse = await axios.get(`${backendUrl}/health`, { timeout: 5000 });
    console.log('✅ Health endpoint responded:', healthResponse.status);
    return true;
  } catch (healthError) {
    console.log('❌ Health endpoint failed:', healthError.message);
    
    try {
      // Try root endpoint
      console.log('📡 Trying root endpoint...');
      const rootResponse = await axios.get(`${backendUrl}/`, { timeout: 5000 });
      console.log('✅ Root endpoint responded:', rootResponse.status);
      return true;
    } catch (rootError) {
      console.log('❌ Root endpoint failed:', rootError.message);
      
      try {
        // Try the actual API endpoint
        console.log('📡 Trying API endpoint...');
        const apiResponse = await axios.get(`${backendUrl}/process-document/csv`, { timeout: 5000 });
        console.log('✅ API endpoint responded:', apiResponse.status);
        return true;
      } catch (apiError) {
        console.log('❌ API endpoint failed:', apiError.message);
        console.log('🔴 Backend server appears to be down or not accessible');
        return false;
      }
    }
  }
}

async function main() {
  console.log('='.repeat(50));
  console.log('Backend Server Health Check');
  console.log('='.repeat(50));
  
  const isRunning = await checkBackend();
  
  console.log('\n' + '='.repeat(50));
  if (isRunning) {
    console.log('✅ BACKEND IS RUNNING - PDF processing should work!');
    console.log('💡 Your frontend should now be able to process PDFs');
  } else {
    console.log('❌ BACKEND IS NOT RUNNING');
    console.log('💡 To get PDF content extraction:');
    console.log('   1. Start your backend server');
    console.log('   2. Make sure it\'s running on localhost:8000');
    console.log('   3. Verify the /process-document/csv endpoint exists');
    console.log('   4. Try uploading your PDF again');
  }
  console.log('='.repeat(50));
}

main().catch(console.error);
