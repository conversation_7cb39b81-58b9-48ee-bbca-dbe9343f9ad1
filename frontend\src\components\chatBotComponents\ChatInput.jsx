import { Send<PERSON><PERSON><PERSON><PERSON>, Loader2, ArrowRight, Database } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { refreshDb } from '../../queries/chatbotHelper';

const ChatInput = ({
  isLoading,
  textInput,
  setTextInput,
  handleSend,
  
}) => {
  const textInputRef = useRef(null);

  // Focus on the textarea when the component mounts or isLoading changes
  useEffect(() => {
    if (textInputRef.current) {
      textInputRef.current.focus();
    }
  }, [isLoading]);

  // Handle Enter key for sending messages (without Shift for new line)
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey && textInput.trim() && !isLoading) {
      e.preventDefault();
      e.target.style.height = 'auto'; ; 
      handleSend();
    }
  };

  // Adjust textarea height dynamically based on content
  const handleInput = (e) => {
    setTextInput(e.target.value);
    e.target.style.height = '0px';
    e.target.style.height = `${Math.min(e.target.scrollHeight, 140)}px`;
  };

  return (
    <div className=" mb-[35px]
      flex justify-center p-4
      relative z-10   gap-7 flex-wrap popup-enter
    ">
       <RefreshButton/> 

      {/* Main Input Bar Container */}
      <div className="
        flex-1 max-w-[700px]
        relative
        flex items-end
        p-2
        /* REMOVED: border border-gray-300 dark:border-gray-600 */
        rounded-xl
          /* Slightly lighter for better contrast without border */
                 /* Increased shadow for more definition without border */
        focus-within:shadow-lg /* More prominent shadow on focus */
        transition-all duration-200 ease-in-out
        focus-within:outline-none 
      shadow-[0_4px_6px_rgba(0,0,0,0.1)]
      filter-[10px]
      shadow-xl

      ">
                {/* focus-within:ring-2 focus-within:ring-blue-500 dark:focus-within:ring-blue-400  Ring for focus   */}

        {/* Text Input */}
      
        <textarea
          ref={textInputRef}
          className="  scrollbar-hide
            flex-1 p-2 pr-10
            bg-transparent
            text-white
            placeholder-gray-400
            focus:outline-none focus:ring-0
            resize-none
            min-h-[.75rem] max-h-[140px] overflow-y-auto
            text-base leading-snug
            filter-[20px]
          "
          value={textInput}
          onChange={handleInput}
          placeholder="Send a message..."
          disabled={isLoading}
          rows={1}
          onKeyDown={handleKeyDown}
        />

        {/* Send Button - Absolutely positioned inside the main input bar */}
      <button
  onClick={handleSend}
  className="
    absolute 
    bottom-2 right-4
    p-2
    bg-gradient-to-r from-pink-600 to-blue-700
    hover:from-blue-500 hover:to-pink-700
    rounded-full
    flex items-center justify-center
    disabled:opacity-40 disabled:cursor-not-allowed
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white
  "
  disabled={isLoading || !textInput?.trim()}
  aria-label="Send message"
>
  {isLoading ? (
    <Loader2 className="animate-spin h-4 w-4 text-white" />
  ) : (
    <ArrowRight className="h-4 w-4 text-white" />
  )}
</button>

      </div>
    </div>
  );
};

export default ChatInput;


export const RefreshButton = () => {
  const [isLoading, setIsLoading] = useState(false); // Track the loading state

  // Handle refresh DB click
  const handleRefreshDb = async () => {
    setIsLoading(true); // Set loading to true when the async function starts
    try {
      await refreshDb(); // Call your actual async function

      // Show success popup
      alert("THE DATABASE IS DELETED");

      // Redirect to first page
      window.location.href = '/';

    } catch (error) {
      console.error("Error refreshing database:", error);
      alert("Error deleting database. Please try again.");
    } finally {
      setIsLoading(false); // Set loading to false when the operation is done
    }
  };

  return (
    <div className="relative group">
      <button
        onClick={handleRefreshDb}
        className="relative z-20 px-4 py-2 text-white bg-gradient-to-r from-red-400 to-red-600 rounded-full text-xl font-semibold shadow-md cursor-pointer hover:shadow-lg hover:scale-105 active:shadow-sm active:scale-95 text-xs"
        disabled={isLoading}
      >
        {/* Show loader when loading, otherwise show Database icon */}
        {isLoading ? (
          <Loader2 className="mx-auto animate-spin" /> // Show loader if loading
        ) : (
          <Database className="mx-auto" /> // Show Database icon if not loading
        )}
        {/* Change button text based on loading state */}
        {isLoading ? "Refreshing..." : "Refresh DB"}
      </button>

      {/* Hover Tooltip */}
      <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-30 group-hover:tooltip-popup">
        <div className="bg-black text-white text-xs rounded-md px-3 py-2 whitespace-nowrap shadow-lg">
          THIS WILL DELETE THE INVOICES IN THE DB
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
        </div>
      </div>
    </div>
  );
};
 