"""
Configuration module for FastAPI OCR application
Loads settings from environment variables and .env file
"""

import os
import json
from typing import List, Union
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from main .env file in parent directory
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
env_file = parent_dir / ".env"

if env_file.exists():
    load_dotenv(env_file)
    print(f"Loaded environment from: {env_file}")
else:
    print(f"WARNING: Main .env file not found at: {env_file}")
    # Fallback to current directory
    load_dotenv()


class Settings:
    """Application settings loaded from environment variables"""
    
    def __init__(self):
        
        self.GROQ_API_KEY: str = os.getenv("GROQ_API_KEY", "")
        

        self.HOST: str = os.getenv("HOST", "0.0.0.0")
        self.PORT: int = int(os.getenv("HANDWRITTEN_MODE_PORT", "8002"))
        self.DEBUG: bool = self._str_to_bool(os.getenv("DEBUG", "False"))
        self.RELOAD: bool = self._str_to_bool(os.getenv("RELOAD", "True"))
        self.LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO").upper()
        
        
        self.APP_NAME: str = os.getenv("APP_NAME", "Handwritten OCR API")
        self.APP_VERSION: str = os.getenv("APP_VERSION", "1.0.0")
        self.APP_DESCRIPTION: str = os.getenv(
            "APP_DESCRIPTION", 
            "API for processing handwritten documents using advanced OCR"
        )
        
        
        self.MAX_FILE_SIZE_MB: int = int(os.getenv("MAX_FILE_SIZE_MB", "50"))
        self.MAX_FILE_SIZE_BYTES: int = self.MAX_FILE_SIZE_MB * 1024 * 1024
        self.MAX_BATCH_FILES: int = int(os.getenv("MAX_BATCH_FILES", "50"))
        
        
        self.ALLOWED_EXTENSIONS: List[str] = self._parse_list(
            os.getenv("ALLOWED_EXTENSIONS", ".jpg,.jpeg,.png,.bmp,.tiff,.tif")
        )
        
        
        self.CORS_ORIGINS: List[str] = self._parse_json_list(
            os.getenv("CORS_ORIGINS", '["*"]')
        )
        self.CORS_ALLOW_CREDENTIALS: bool = self._str_to_bool(
            os.getenv("CORS_ALLOW_CREDENTIALS", "True")
        )
        self.CORS_ALLOW_METHODS: List[str] = self._parse_json_list(
            os.getenv("CORS_ALLOW_METHODS", '["*"]')
        )
        self.CORS_ALLOW_HEADERS: List[str] = self._parse_json_list(
            os.getenv("CORS_ALLOW_HEADERS", '["*"]')
        )
        
        
        self.INPUT_DIR: str = os.getenv("INPUT_DIR", "input_images")
        self.OUTPUT_DIR: str = os.getenv("OUTPUT_DIR", "output")
        
        
        self.DEFAULT_SECTIONS: int = int(os.getenv("DEFAULT_SECTIONS", "3"))
        self.MAX_SECTIONS: int = int(os.getenv("MAX_SECTIONS", "6"))
        self.SECTION_OVERLAP: float = float(os.getenv("SECTION_OVERLAP", "0.2"))
        
        
        self.TASK_TIMEOUT_SECONDS: int = int(os.getenv("TASK_TIMEOUT_SECONDS", "300"))
        self.MAX_CONCURRENT_TASKS: int = int(os.getenv("MAX_CONCURRENT_TASKS", "5"))
        
        
        self.DOCS_URL: str = os.getenv("DOCS_URL", "/docs")
        self.REDOC_URL: str = os.getenv("REDOC_URL", "/redoc")
        self.OPENAPI_URL: str = os.getenv("OPENAPI_URL", "/openapi.json")
        
        
        self.API_KEY_HEADER: str = os.getenv("API_KEY_HEADER", "X-API-Key")
        self.REQUIRE_API_KEY: bool = self._str_to_bool(os.getenv("REQUIRE_API_KEY", "False"))
        self.ALLOWED_API_KEYS: List[str] = self._parse_list(
            os.getenv("ALLOWED_API_KEYS", "")
        )
        
        
        self.RATE_LIMIT_ENABLED: bool = self._str_to_bool(os.getenv("RATE_LIMIT_ENABLED", "False"))
        self.RATE_LIMIT_REQUESTS: int = int(os.getenv("RATE_LIMIT_REQUESTS", "100"))
        self.RATE_LIMIT_WINDOW: int = int(os.getenv("RATE_LIMIT_WINDOW", "3600"))  
        
        
        self._validate_settings()
    
    @staticmethod
    def _str_to_bool(value: str) -> bool:
        """Convert string to boolean"""
        return value.lower() in ("true", "1", "yes", "on")
    
    @staticmethod
    def _parse_list(value: str, delimiter: str = ",") -> List[str]:
        """Parse comma-separated string to list"""
        if not value:
            return []
        return [item.strip() for item in value.split(delimiter) if item.strip()]
    
    @staticmethod
    def _parse_json_list(value: str) -> List[str]:
        """Parse JSON string to list"""
        try:
            result = json.loads(value)
            return result if isinstance(result, list) else [str(result)]
        except (json.JSONDecodeError, TypeError):
            return [value] if value else []
    
    def _validate_settings(self):
        """Validate required settings"""
        if not self.GROQ_API_KEY:
            raise ValueError("GROQ_API_KEY is required but not set")
        
        if self.PORT < 1 or self.PORT > 65535:
            raise ValueError(f"PORT must be between 1 and 65535, got {self.PORT}")
        
        if self.MAX_FILE_SIZE_MB < 1:
            raise ValueError(f"MAX_FILE_SIZE_MB must be at least 1, got {self.MAX_FILE_SIZE_MB}")
        
        if self.LOG_LEVEL not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError(f"Invalid LOG_LEVEL: {self.LOG_LEVEL}")
    
    def get_uvicorn_config(self) -> dict:
        """Get configuration for uvicorn server"""
        return {
            "host": self.HOST,
            "port": self.PORT,
            "reload": self.RELOAD,
            "log_level": self.LOG_LEVEL.lower(),
            "access_log": self.DEBUG,
        }
    
    def get_cors_config(self) -> dict:
        """Get CORS configuration"""
        return {
            "allow_origins": self.CORS_ORIGINS,
            "allow_credentials": self.CORS_ALLOW_CREDENTIALS,
            "allow_methods": self.CORS_ALLOW_METHODS,
            "allow_headers": self.CORS_ALLOW_HEADERS,
        }
    
    def get_app_config(self) -> dict:
        """Get FastAPI app configuration"""
        return {
            "title": self.APP_NAME,
            "description": self.APP_DESCRIPTION,
            "version": self.APP_VERSION,
            "docs_url": self.DOCS_URL if not self.REQUIRE_API_KEY else None,
            "redoc_url": self.REDOC_URL if not self.REQUIRE_API_KEY else None,
            "openapi_url": self.OPENAPI_URL,
        }
    
    def create_directories(self):
        """Create necessary directories"""
        Path(self.INPUT_DIR).mkdir(exist_ok=True)
        Path(self.OUTPUT_DIR).mkdir(exist_ok=True)
    
    def print_config(self):
        """Print current configuration (excluding sensitive data)"""
        print("Current Configuration:")
        print(f"   App Name: {self.APP_NAME}")
        print(f"   Version: {self.APP_VERSION}")
        print(f"   Host: {self.HOST}")
        print(f"   Port: {self.PORT}")
        print(f"   Debug: {self.DEBUG}")
        print(f"   Reload: {self.RELOAD}")
        print(f"   Log Level: {self.LOG_LEVEL}")
        print(f"   Max File Size: {self.MAX_FILE_SIZE_MB}MB")
        print(f"   Max Batch Files: {self.MAX_BATCH_FILES}")
        print(f"   CORS Origins: {self.CORS_ORIGINS}")
        print(f"   Input Directory: {self.INPUT_DIR}")
        print(f"   Output Directory: {self.OUTPUT_DIR}")
        print(f"   API Key Required: {self.REQUIRE_API_KEY}")
        print(f"   Rate Limiting: {self.RATE_LIMIT_ENABLED}")



settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance"""
    return settings
