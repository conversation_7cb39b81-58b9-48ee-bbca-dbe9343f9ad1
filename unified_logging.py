"""
Unified Logging System for Agentic Document Processing

This module provides comprehensive logging functionality for all components:
- Structured logging with JSON format
- Multiple log levels and handlers
- Processing event tracking
- Error monitoring and alerting
- Performance metrics logging
- Agent activity logging
"""

import os
import json
import logging
import logging.handlers
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
import uuid
import time
from functools import wraps

from config import settings

# ================================
# LOGGING CONFIGURATION
# ================================

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry)

class UnifiedLogger:
    """
    Unified logging system for the entire application
    """
    
    def __init__(self, name: str = "agentic_processor"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, settings.log_level))
        
        # Ensure logs directory exists
        logs_dir = Path(settings.logs_dir)
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup handlers
        self._setup_handlers()
        
        # Processing session tracking
        self.current_session = None
        self.session_start_time = None
        
    def _setup_handlers(self):
        """Setup logging handlers"""
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Console handler with colored output
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(getattr(logging, settings.log_level))
        self.logger.addHandler(console_handler)
        
        # File handler for general logs
        log_file = Path(settings.logs_dir) / f"{self.name}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(JSONFormatter())
        file_handler.setLevel(logging.INFO)
        self.logger.addHandler(file_handler)
        
        # Error file handler
        error_log_file = Path(settings.logs_dir) / f"{self.name}_errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        error_handler.setFormatter(JSONFormatter())
        error_handler.setLevel(logging.ERROR)
        self.logger.addHandler(error_handler)
        
        # Processing logs handler
        if settings.enable_detailed_logging:
            processing_log_file = Path(settings.processing_logs_dir) / f"processing_{datetime.now().strftime('%Y%m%d')}.jsonl"
            processing_handler = logging.handlers.RotatingFileHandler(
                processing_log_file,
                maxBytes=50*1024*1024,  # 50MB
                backupCount=10
            )
            processing_handler.setFormatter(JSONFormatter())
            processing_handler.setLevel(logging.INFO)
            self.logger.addHandler(processing_handler)
    
    def start_session(self, session_type: str = "processing") -> str:
        """Start a new logging session"""
        self.current_session = str(uuid.uuid4())
        self.session_start_time = time.time()
        
        self.log_event("session_start", f"Started {session_type} session", {
            "session_id": self.current_session,
            "session_type": session_type
        })
        
        return self.current_session
    
    def end_session(self, success: bool = True, summary: Optional[Dict[str, Any]] = None):
        """End the current logging session"""
        if self.current_session:
            session_duration = time.time() - self.session_start_time if self.session_start_time else 0
            
            self.log_event("session_end", f"Session ended: {'success' if success else 'failure'}", {
                "session_id": self.current_session,
                "duration_seconds": session_duration,
                "success": success,
                "summary": summary or {}
            })
            
            self.current_session = None
            self.session_start_time = None
    
    def log_event(self, event_type: str, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """Log a structured event"""
        extra_fields = {
            "event_type": event_type,
            "session_id": self.current_session,
            **(extra_data or {})
        }
        
        # Create log record with extra fields
        record = self.logger.makeRecord(
            self.logger.name,
            logging.INFO,
            __file__,
            0,
            message,
            (),
            None
        )
        record.extra_fields = extra_fields
        
        self.logger.handle(record)
    
    def log_document_processing(self, filename: str, processing_mode: str, status: str, 
                              processing_time: float, metadata: Optional[Dict[str, Any]] = None):
        """Log document processing event"""
        self.log_event("document_processing", f"Document processed: {filename}", {
            "filename": filename,
            "processing_mode": processing_mode,
            "status": status,
            "processing_time": processing_time,
            "metadata": metadata or {}
        })
    
    def log_agent_activity(self, agent_name: str, action: str, details: Optional[Dict[str, Any]] = None):
        """Log AutoGen agent activity"""
        self.log_event("agent_activity", f"Agent {agent_name}: {action}", {
            "agent_name": agent_name,
            "action": action,
            "details": details or {}
        })
    
    def log_api_request(self, endpoint: str, method: str, status_code: int, 
                       response_time: float, user_agent: Optional[str] = None):
        """Log API request"""
        self.log_event("api_request", f"{method} {endpoint} -> {status_code}", {
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "response_time": response_time,
            "user_agent": user_agent
        })
    
    def log_error(self, error_type: str, error_message: str, context: Optional[Dict[str, Any]] = None):
        """Log error with context"""
        extra_fields = {
            "error_type": error_type,
            "context": context or {},
            "session_id": self.current_session
        }
        
        record = self.logger.makeRecord(
            self.logger.name,
            logging.ERROR,
            __file__,
            0,
            error_message,
            (),
            None
        )
        record.extra_fields = extra_fields
        
        self.logger.handle(record)
    
    def log_performance(self, operation: str, duration: float, metadata: Optional[Dict[str, Any]] = None):
        """Log performance metrics"""
        self.log_event("performance", f"Operation {operation} took {duration:.2f}s", {
            "operation": operation,
            "duration": duration,
            "metadata": metadata or {}
        })
    
    def log_rag_activity(self, activity_type: str, query: Optional[str] = None, 
                        results_count: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None):
        """Log RAG chatbot activity"""
        self.log_event("rag_activity", f"RAG {activity_type}", {
            "activity_type": activity_type,
            "query": query,
            "results_count": results_count,
            "metadata": metadata or {}
        })
    
    def info(self, message: str, **kwargs):
        """Standard info logging"""
        self.logger.info(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs):
        """Standard warning logging"""
        self.logger.warning(message, extra=kwargs)
    
    def error(self, message: str, **kwargs):
        """Standard error logging"""
        self.logger.error(message, extra=kwargs)
    
    def debug(self, message: str, **kwargs):
        """Standard debug logging"""
        self.logger.debug(message, extra=kwargs)

# ================================
# DECORATORS
# ================================

def log_execution_time(logger: UnifiedLogger, operation_name: str):
    """Decorator to log execution time of functions"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.log_performance(f"{operation_name}::{func.__name__}", duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.log_error(f"{operation_name}_error", str(e), {
                    "function": func.__name__,
                    "duration": duration
                })
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.log_performance(f"{operation_name}::{func.__name__}", duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.log_error(f"{operation_name}_error", str(e), {
                    "function": func.__name__,
                    "duration": duration
                })
                raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

# ================================
# GLOBAL LOGGER INSTANCES
# ================================

# Main application logger
main_logger = UnifiedLogger("main")

# Component-specific loggers
agent_logger = UnifiedLogger("agents")
processor_logger = UnifiedLogger("processors")
api_logger = UnifiedLogger("api")
rag_logger = UnifiedLogger("rag")

# Export commonly used loggers
__all__ = [
    'UnifiedLogger',
    'main_logger',
    'agent_logger', 
    'processor_logger',
    'api_logger',
    'rag_logger',
    'log_execution_time'
]
