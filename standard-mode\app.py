"""
FastAPI Application for Standard Mode Document Processing

This module provides a FastAPI-based web service for processing structured documents,
primarily PDFs and typed documents. It offers multi-language support, intelligent
text extraction, and structured data parsing using Groq LLM technology.

Key Features:
- PDF text extraction with layout preservation
- Multi-language support (80+ languages with auto-detection)
- Intelligent data field extraction using LLM
- Batch processing capabilities
- OCR fallback for image-based PDFs
- Comprehensive error handling and logging
- RESTful API with automatic documentation

Architecture:
- Receives documents from Bridge API or direct clients
- Extracts text using PDF parsing or OCR
- Analyzes content using Groq LLM for structured data extraction
- Returns standardized CSV format with invoice/document data
- Provides health checks and system status information
"""

# Standard library imports for core functionality
import tempfile  # Temporary file handling for document processing
from pathlib import Path  # Modern path handling utilities
from typing import List, Optional, Dict, Any  # Type hints for better code clarity
import logging  # Logging framework for debugging and monitoring
import asyncio  # Asynchronous programming support
from datetime import datetime  # Date/time operations for timestamps

# FastAPI framework imports for web API functionality
from fastapi import FastAPI, File, UploadFile, HTTPException, Form  # Core FastAPI components
from fastapi.responses import StreamingResponse  # Streaming response for file downloads
from fastapi.middleware.cors import CORSMiddleware  # Cross-origin resource sharing
from pydantic import BaseModel, Field  # Data validation and serialization
import uvicorn  # ASGI server for running the FastAPI application

# Local module imports for application functionality
from parser import DocumentParser  # Core document parsing engine
from models import DocumentParsingResult  # Pydantic models for data validation
from config import get_settings  # Configuration management

# Load application settings from environment variables
settings = get_settings()

# Initialize logging with configured level
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)  # Create logger instance for this module


app = FastAPI(
    title=settings.app_name,
    description="AI-powered invoice and receipt parsing API with multi-language support",
    version=settings.app_version,
    docs_url="/docs",
    redoc_url="/redoc",
    debug=settings.debug
)


app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)


parser_instance: Optional[DocumentParser] = None


class ProcessingOptions(BaseModel):
    language: Optional[str] = Field(None, description="Language hint (e.g., 'en', 'es', 'fr')")
    auto_detect_language: bool = Field(True, description="Enable automatic language detection")
    enhanced_image_mode: bool = Field(False, description="Use enhanced image processing mode")
    save_raw_text: bool = Field(False, description="Include raw extracted text in response")

class ProcessingResponse(BaseModel):
    success: bool
    message: str
    results: List[Dict[str, Any]]
    processing_time: float
    files_processed: int
    files_successful: int
    files_failed: int

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    groq_api_configured: bool


@app.on_event("startup")
async def startup_event():
    """Initialize the document parser on startup."""
    global parser_instance
    
    logger.info("Starting FastAPI Document Parser...")

    if not settings.groq_api_key:
        logger.error("GROQ_API_KEY environment variable not set!")
        raise RuntimeError("GROQ_API_KEY environment variable is required")

    try:
        logger.info(f"Initializing parser with API key: {settings.groq_api_key[:10]}...{settings.groq_api_key[-4:]}")
        parser_instance = DocumentParser(
            groq_api_key=settings.groq_api_key,
            auto_detect_language=settings.default_auto_detect_language,
            preserve_original_language=settings.preserve_original_language
        )
        logger.info("Document parser initialized successfully - preserving original language")
    except Exception as e:
        logger.error(f"Failed to initialize document parser: {e}")
        raise RuntimeError(f"Failed to initialize document parser: {e}")


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy" if parser_instance else "unhealthy",
        timestamp=datetime.now().isoformat(),
        groq_api_configured=bool(settings.groq_api_key)
    )


@app.get("/languages")
async def get_supported_languages():
    """Get list of supported languages."""
    languages = {
        "European Languages": {
            'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German', 'it': 'Italian',
            'pt': 'Portuguese', 'ru': 'Russian', 'pl': 'Polish', 'nl': 'Dutch', 'sv': 'Swedish',
            'da': 'Danish', 'no': 'Norwegian', 'fi': 'Finnish', 'cs': 'Czech', 'sk': 'Slovak',
            'hu': 'Hungarian', 'ro': 'Romanian', 'bg': 'Bulgarian', 'hr': 'Croatian', 'sr': 'Serbian',
            'sl': 'Slovenian', 'et': 'Estonian', 'lv': 'Latvian', 'lt': 'Lithuanian', 'uk': 'Ukrainian',
            'be': 'Belarusian', 'mk': 'Macedonian', 'sq': 'Albanian', 'eu': 'Basque', 'ca': 'Catalan',
            'gl': 'Galician', 'cy': 'Welsh', 'ga': 'Irish', 'mt': 'Maltese', 'is': 'Icelandic', 'fo': 'Faroese'
        },
        "Asian Languages": {
            'zh': 'Chinese', 'ja': 'Japanese', 'ko': 'Korean', 'th': 'Thai', 'vi': 'Vietnamese',
            'hi': 'Hindi', 'bn': 'Bengali', 'gu': 'Gujarati', 'pa': 'Punjabi', 'ta': 'Tamil',
            'te': 'Telugu', 'kn': 'Kannada', 'ml': 'Malayalam', 'si': 'Sinhala', 'my': 'Myanmar',
            'km': 'Khmer', 'lo': 'Lao', 'mn': 'Mongolian', 'ne': 'Nepali', 'mr': 'Marathi',
            'or': 'Odia', 'as': 'Assamese'
        },
        "Middle Eastern & Central Asian": {
            'ar': 'Arabic', 'he': 'Hebrew', 'fa': 'Persian/Farsi', 'ur': 'Urdu', 'ku': 'Kurdish',
            'ps': 'Pashto', 'am': 'Amharic', 'ti': 'Tigrinya', 'ka': 'Georgian', 'hy': 'Armenian',
            'az': 'Azerbaijani', 'kk': 'Kazakh', 'ky': 'Kyrgyz', 'uz': 'Uzbek', 'tg': 'Tajik'
        },
        "African Languages": {
            'sw': 'Swahili', 'zu': 'Zulu', 'xh': 'Xhosa', 'af': 'Afrikaans', 'yo': 'Yoruba',
            'ig': 'Igbo', 'ha': 'Hausa', 'om': 'Oromo', 'so': 'Somali', 'mg': 'Malagasy',
            'ny': 'Chichewa', 'sn': 'Shona', 'st': 'Sesotho', 'tn': 'Tswana', 've': 'Venda',
            'ts': 'Tsonga', 'ss': 'Swati', 'nr': 'Ndebele'
        },
        "Southeast Asian & Pacific": {
            'ms': 'Malay', 'id': 'Indonesian', 'tl': 'Filipino/Tagalog', 'ceb': 'Cebuano',
            'jv': 'Javanese', 'su': 'Sundanese'
        }
    }
    
    return {
        "supported_languages": languages,
        "total_languages": sum(len(category) for category in languages.values()),
        "usage_examples": [
            "Use 'en' for English documents",
            "Use 'es' for Spanish documents", 
            "Use 'zh' for Chinese documents",
            "Use 'ar' for Arabic documents",
            "Leave empty for automatic detection"
        ]
    }

def validate_file_type(filename: str) -> bool:
    """Validate if the file type is supported."""
    file_ext = Path(filename).suffix.lower()
    return file_ext in settings.supported_file_extensions

async def save_uploaded_file(upload_file: UploadFile, temp_dir: Path) -> Path:
    """Save uploaded file to temporary directory."""
    if not validate_file_type(upload_file.filename):
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type. Supported: PDF, PNG, JPG, JPEG, TIFF, BMP, TXT, WEBP, GIF"
        )
    
    file_path = temp_dir / upload_file.filename
    
    try:
        with open(file_path, "wb") as buffer:
            content = await upload_file.read()
            buffer.write(content)
        return file_path
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")

async def process_document_async(file_path: Path, options: ProcessingOptions) -> DocumentParsingResult:
    """Process a single document asynchronously."""
    if not parser_instance:
        raise HTTPException(status_code=500, detail="Parser not initialized")
    
    try:
        logger.info(f"Starting async processing for {file_path} with language: {options.language}")

        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            parser_instance.parse_document,
            file_path,
            options.language
        )

        logger.info(f"Processing completed for {file_path}: success={result.success}, method={result.extraction_method}")
        return result
    except Exception as e:
        logger.error(f"Error processing document {file_path}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

def format_result_for_api(result: DocumentParsingResult, include_raw_text: bool = False) -> Dict[str, Any]:
    """Format DocumentParsingResult for API response."""
    formatted = {
        "success": result.success,
        "error_message": result.error_message,
        "processing_time": result.processing_time,
        "extraction_method": result.extraction_method,
        "invoice_data": result.invoice_data.model_dump() if result.invoice_data else None
    }
    
    if include_raw_text and result.raw_text:
        formatted["raw_text"] = result.raw_text
    
    return formatted


@app.post("/process-document", response_model=ProcessingResponse)
async def process_single_document(
    file: UploadFile = File(..., description="Document file to process"),
    language: Optional[str] = Form(None, description="Language hint (e.g., 'en', 'es', 'fr')"),
    auto_detect_language: bool = Form(True, description="Enable automatic language detection"),
    enhanced_image_mode: bool = Form(False, description="Use enhanced image processing mode"),
    save_raw_text: bool = Form(False, description="Include raw extracted text in response")
):
    """Process a single document file."""
    import time
    start_time = time.time()

    
    options = ProcessingOptions(
        language=language,
        auto_detect_language=auto_detect_language,
        enhanced_image_mode=enhanced_image_mode,
        save_raw_text=save_raw_text
    )

    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        try:
            
            file_path = await save_uploaded_file(file, temp_path)

            
            result = await process_document_async(file_path, options)

            
            processing_time = time.time() - start_time
            formatted_result = format_result_for_api(result, options.save_raw_text)

            return ProcessingResponse(
                success=result.success,
                message="Document processed successfully" if result.success else f"Processing failed: {result.error_message}",
                results=[formatted_result],
                processing_time=processing_time,
                files_processed=1,
                files_successful=1 if result.success else 0,
                files_failed=0 if result.success else 1
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Unexpected error processing document: {e}")
            raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


@app.post("/process-batch", response_model=ProcessingResponse)
async def process_batch_documents(
    files: List[UploadFile] = File(..., description="Multiple document files to process"),
    language: Optional[str] = Form(None, description="Language hint for all documents"),
    auto_detect_language: bool = Form(True, description="Enable automatic language detection"),
    enhanced_image_mode: bool = Form(False, description="Use enhanced image processing mode"),
    save_raw_text: bool = Form(False, description="Include raw extracted text in response")
):
    """Process multiple document files in batch."""
    import time
    start_time = time.time()

    if len(files) > settings.max_batch_files:
        raise HTTPException(status_code=400, detail=f"Maximum {settings.max_batch_files} files allowed per batch")

    
    options = ProcessingOptions(
        language=language,
        auto_detect_language=auto_detect_language,
        enhanced_image_mode=enhanced_image_mode,
        save_raw_text=save_raw_text
    )

    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        results = []
        successful = 0
        failed = 0

        try:
            
            for file in files:
                try:
                    
                    file_path = await save_uploaded_file(file, temp_path)

                    
                    result = await process_document_async(file_path, options)

                    
                    formatted_result = format_result_for_api(result, options.save_raw_text)
                    formatted_result["filename"] = file.filename
                    results.append(formatted_result)

                    if result.success:
                        successful += 1
                    else:
                        failed += 1

                except Exception as e:
                    logger.error(f"Error processing file {file.filename}: {e}")
                    error_result = {
                        "filename": file.filename,
                        "success": False,
                        "error_message": str(e),
                        "processing_time": 0,
                        "extraction_method": None,
                        "invoice_data": None
                    }
                    results.append(error_result)
                    failed += 1

            processing_time = time.time() - start_time

            return ProcessingResponse(
                success=successful > 0,
                message=f"Batch processing completed. {successful} successful, {failed} failed.",
                results=results,
                processing_time=processing_time,
                files_processed=len(files),
                files_successful=successful,
                files_failed=failed
            )

        except Exception as e:
            logger.error(f"Unexpected error in batch processing: {e}")
            raise HTTPException(status_code=500, detail=f"Batch processing failed: {str(e)}")


@app.post("/process-document/csv")
async def process_document_and_download_csv(
    file: UploadFile = File(..., description="Document file to process"),
    language: Optional[str] = Form(None, description="Language hint"),
    auto_detect_language: bool = Form(True, description="Enable automatic language detection")
):
    """Process a document and return results as CSV file."""
    import csv
    import io
    from fastapi.responses import StreamingResponse

    options = ProcessingOptions(
        language=language,
        auto_detect_language=auto_detect_language
    )

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        try:
            
            file_path = await save_uploaded_file(file, temp_path)
            result = await process_document_async(file_path, options)

            if not result.success:
                raise HTTPException(status_code=400, detail=f"Processing failed: {result.error_message or 'Unknown error'}")

            if not result.invoice_data:
                raise HTTPException(status_code=400, detail="No invoice data extracted from document")

            
            csv_content = io.StringIO()
            csv_rows = result.invoice_data.to_csv_rows()

            if not csv_rows:
                raise HTTPException(status_code=400, detail="No data available for CSV export")

            writer = csv.DictWriter(csv_content, fieldnames=csv_rows[0].keys())
            writer.writeheader()
            writer.writerows(csv_rows)

            
            csv_string = csv_content.getvalue()
            filename = f"{Path(file.filename).stem}_results.csv"

            return StreamingResponse(
                io.BytesIO(csv_string.encode('utf-8')),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error generating CSV for file {file.filename}: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"CSV generation failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
