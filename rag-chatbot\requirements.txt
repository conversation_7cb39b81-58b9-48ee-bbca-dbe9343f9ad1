# RAG Chatbot API Requirements

# Core Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
python-dotenv>=1.0.0
python-multipart>=0.0.6

# LLM and AI Services
groq>=0.4.0
langchain-groq>=0.1.0

# LangChain Framework
langchain>=0.1.0
langchain-core>=0.1.0
langchain-community>=0.0.20

# Vector Store and Embeddings
chromadb>=0.4.0
langchain-chroma>=0.1.0
sentence-transformers>=2.2.0
langchain-huggingface>=0.0.3

# Data Processing
pandas>=2.0.0
numpy>=1.24.0

# Web and Networking
requests>=2.31.0
httpx>=0.25.0

# Utilities
pathlib2>=2.3.7
typing-extensions>=4.8.0
