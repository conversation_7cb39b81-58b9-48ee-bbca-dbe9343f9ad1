<!DOCTYPE html>
<html>
<head>
    <title>Backend Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 10px 0; font-size: 16px; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>Backend Server Connection Test</h1>
    <p>This will test if your backend server is running and accessible.</p>
    
    <button onclick="testBackendConnection()">Test Backend Connection</button>
    <button onclick="testFileUpload()">Test File Upload</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        const backendUrl = 'http://localhost:8000';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            results.appendChild(div);
            console.log(message);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testBackendConnection() {
            addResult('🧪 Starting backend connection test...', 'info');
            
            // Test 1: Basic connectivity
            try {
                addResult('📡 Testing basic connectivity...', 'info');
                const response = await fetch(`${backendUrl}/`, { 
                    method: 'GET',
                    mode: 'cors'
                });
                addResult(`✅ Basic connectivity OK: ${response.status} ${response.statusText}`, 'success');
            } catch (error) {
                addResult(`❌ Basic connectivity failed: ${error.message}`, 'error');
            }
            
            // Test 2: Health endpoint
            try {
                addResult('📡 Testing health endpoint...', 'info');
                const response = await fetch(`${backendUrl}/health`, { 
                    method: 'GET',
                    mode: 'cors'
                });
                addResult(`✅ Health endpoint OK: ${response.status} ${response.statusText}`, 'success');
            } catch (error) {
                addResult(`❌ Health endpoint failed: ${error.message}`, 'error');
            }
            
            // Test 3: API endpoint
            try {
                addResult('📡 Testing API endpoint...', 'info');
                const response = await fetch(`${backendUrl}/process-document/csv`, { 
                    method: 'GET',
                    mode: 'cors'
                });
                addResult(`✅ API endpoint responded: ${response.status} ${response.statusText}`, 'success');
            } catch (error) {
                addResult(`❌ API endpoint failed: ${error.message}`, 'error');
            }
            
            addResult('🧪 Backend connection test complete', 'info');
        }
        
        async function testFileUpload() {
            addResult('📤 Starting file upload test...', 'info');
            
            // Create a test file
            const testContent = 'Test file content for backend upload test';
            const testFile = new Blob([testContent], { type: 'text/plain' });
            const formData = new FormData();
            formData.append('file', testFile, 'test.txt');
            
            try {
                addResult('📡 Sending test file to backend...', 'info');
                const response = await fetch(`${backendUrl}/process-document/csv`, {
                    method: 'POST',
                    body: formData,
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const result = await response.blob();
                    addResult(`✅ File upload successful: ${response.status}, received ${result.size} bytes`, 'success');
                    addResult(`📄 Response type: ${result.type}`, 'info');
                } else {
                    addResult(`❌ File upload failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ File upload error: ${error.message}`, 'error');
            }
            
            addResult('📤 File upload test complete', 'info');
        }
    </script>
</body>
</html>
