import React, { useEffect, useRef } from 'react';

const ParticleEffect = ({ className = "" }) => {
  const containerRef = useRef(null);
  const canvasRef = useRef(null);
  const glowCanvasRef = useRef(null);
  const animationRef = useRef(null);
  const mouseRef = useRef({ x: 0, y: 0, isOnCanvas: false });
  const particlePropsRef = useRef(null);
  const tickRef = useRef(0);

  const config = {
    particleCount: 90,
    particlePropCount: 9,
    baseTTL: 1,
    rangeTTL: 2,
    baseSpeed: 0.0008, // Slightly slower for smoother motion
    rangeSpeed: 0.0015,
    circularSpeed: 0.0006, // Reduced for more fluid circular motion
    baseRadius: 2,
    rangeRadius: 3,
    baseHue: 200,
    rangeHue: 120,
    backgroundColor: 'transparent',
    circleRadius: 180,
    glowStrength: 10,
    randomnessFactor: 2, // Reduced for smoother patterns
    trailLength: 0.9, // Increased for smoother trails
    mouseForce: 2, // Increased for more responsive interaction
    mouseRadius: 160,
    smoothingFactor: 0.15 // Added for smoother interpolation
  };

  const TAU = Math.PI * 2;

  const rand = (n) => Math.random() * n;

  const initParticles = (canvas) => {
    particlePropsRef.current = new Float32Array(config.particleCount * config.particlePropCount);
    const angleIncrement = TAU / config.particleCount;

    for (let i = 0; i < config.particleCount; i++) {
      initParticle(i * config.particlePropCount, i * angleIncrement, canvas);
    }
  };

  const initParticle = (i, angleOffset, canvas) => {
    const radius = config.baseRadius + rand(config.rangeRadius);
    const hue = config.baseHue + rand(config.rangeHue);

    particlePropsRef.current.set([
      Math.cos(angleOffset) * config.circleRadius + canvas.width / 2,
      Math.sin(angleOffset) * config.circleRadius + canvas.height / 2,
      0, 0, 0,
      config.baseTTL + rand(config.rangeTTL),
      config.baseSpeed + rand(config.rangeSpeed),
      radius, hue
    ], i);
  };

  const updateParticle = (i, centerX, centerY, ctx) => {
    const angle = tickRef.current * config.circularSpeed + (i / config.particlePropCount) * TAU / config.particleCount;
    const scatterX = Math.sin(tickRef.current * 0.05 + i) * 8 * config.randomnessFactor;
    const scatterY = Math.cos(tickRef.current * 0.05 + i) * 8 * config.randomnessFactor;

    let x = Math.cos(angle) * config.circleRadius + centerX + scatterX;
    let y = Math.sin(angle) * config.circleRadius + centerY + scatterY;

    if (mouseRef.current.isOnCanvas) {
      const dx = mouseRef.current.x - x;
      const dy = mouseRef.current.y - y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < config.mouseRadius) {
        const force = (1 - distance / config.mouseRadius) * config.mouseForce;
        x += dx * force;
        y += dy * force;
      }
    }

    particlePropsRef.current[i] = x;
    particlePropsRef.current[i + 1] = y;

    drawParticle(ctx, x, y, particlePropsRef.current[i + 7], particlePropsRef.current[i + 8]);
  };

  const drawParticle = (ctx, x, y, radius, hue) => {
    ctx.save();
    ctx.fillStyle = `hsla(${hue},100%,60%,${config.trailLength})`;
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, TAU);
    ctx.fill();
    ctx.restore();
  };

  const drawParticles = (canvas, ctx) => {
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    for (let i = 0; i < config.particleCount * config.particlePropCount; i += config.particlePropCount) {
      updateParticle(i, centerX, centerY, ctx);
    }
  };

  const renderGlow = (canvas, ctx, glowCanvas, glowCtx) => {
    const blur1 = `blur(${config.glowStrength}px) brightness(200%)`;
    const blur2 = `blur(${config.glowStrength / 2}px) brightness(200%)`;

    glowCtx.save();
    glowCtx.filter = blur1;
    glowCtx.globalCompositeOperation = 'lighter';
    glowCtx.drawImage(canvas, 0, 0);
    glowCtx.restore();

    glowCtx.save();
    glowCtx.filter = blur2;
    glowCtx.globalCompositeOperation = 'lighter';
    glowCtx.drawImage(canvas, 0, 0);
    glowCtx.restore();
  };

  const renderToScreen = (canvas, glowCtx) => {
    glowCtx.save();
    glowCtx.globalCompositeOperation = 'lighter';
    glowCtx.drawImage(canvas, 0, 0);
    glowCtx.restore();
  };

  const draw = () => {
    const canvas = canvasRef.current;
    const glowCanvas = glowCanvasRef.current;
    if (!canvas || !glowCanvas) return;

    const ctx = canvas.getContext('2d');
    const glowCtx = glowCanvas.getContext('2d');

    tickRef.current++;
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    glowCtx.clearRect(0, 0, glowCanvas.width, glowCanvas.height);

    drawParticles(canvas, ctx);
    renderGlow(canvas, ctx, glowCanvas, glowCtx);
    renderToScreen(canvas, glowCtx);

    animationRef.current = requestAnimationFrame(draw);
  };

  const handleMouseMove = (e) => {
    const rect = canvasRef.current.getBoundingClientRect();
    mouseRef.current.x = e.clientX - rect.left;
    mouseRef.current.y = e.clientY - rect.top;
  };

  const handleMouseEnter = () => {
    mouseRef.current.isOnCanvas = true;
  };

  const handleMouseLeave = () => {
    mouseRef.current.isOnCanvas = false;
  };

  const resize = () => {
    const container = containerRef.current;
    if (!container) return;

    const canvas = canvasRef.current;
    const glowCanvas = glowCanvasRef.current;
    
    const { offsetWidth, offsetHeight } = container;
    
    canvas.width = offsetWidth;
    canvas.height = offsetHeight;
    glowCanvas.width = offsetWidth;
    glowCanvas.height = offsetHeight;

    if (particlePropsRef.current) {
      initParticles(canvas);
    }
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    const glowCanvas = glowCanvasRef.current;
    
    if (!canvas || !glowCanvas) return;

    resize();
    initParticles(canvas);
    draw();

    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseenter', handleMouseEnter);
    canvas.addEventListener('mouseleave', handleMouseLeave);
    window.addEventListener('resize', resize);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseenter', handleMouseEnter);
      canvas.removeEventListener('mouseleave', handleMouseLeave);
      window.removeEventListener('resize', resize);
    };
  }, []);

  return (
    <div 
      ref={containerRef}
      className={`particle-effect-container ${className}`}
      style={{ position: 'absolute', inset: 0, pointerEvents: 'none' }}
    >
      <canvas
        ref={canvasRef}
        style={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          pointerEvents: 'auto',
          zIndex: 1
        }}
      />
      <canvas
        ref={glowCanvasRef}
        style={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          pointerEvents: 'none',
          zIndex: 2
        }}
      />
    </div>
  );
};

export default ParticleEffect;
