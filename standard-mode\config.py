"""
Configuration Management for Standard Mode API

This module provides centralized configuration management for the Standard Mode API
using Pydantic settings with environment variable support. It handles all application
settings, API configuration, and processing parameters.

Key Features:
- Environment variable integration with fallback defaults
- Type validation and conversion using Pydantic
- Centralized configuration management
- Support for development and production environments
- Automatic configuration validation on startup

Configuration Categories:
- Application Settings: Basic app metadata and debug settings
- Server Settings: Host, port, and server configuration
- API Settings: Groq API key and external service configuration
- Processing Settings: File size limits, batch processing, language settings
- Logging Settings: Log levels and output configuration
"""

# Standard library imports for configuration management
import os  # Operating system interface for environment variables
from typing import Optional  # Type hints for optional configuration values
from pathlib import Path  # Path handling for cross-platform compatibility
from pydantic import Field  # Field definitions with validation and defaults
from pydantic_settings import BaseSettings  # Base class for settings management
from dotenv import load_dotenv  # Environment variable loading from .env files

# Load environment variables from main .env file in parent directory
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
env_file = parent_dir / ".env"

if env_file.exists():
    load_dotenv(env_file)
    print(f"Loaded environment from: {env_file}")
else:
    print(f"WARNING: Main .env file not found at: {env_file}")
    # Fallback to current directory
    load_dotenv()


class Settings(BaseSettings):
    """
    Application Settings with Environment Variable Support

    This class defines all configuration parameters for the Standard Mode API
    with automatic environment variable binding and type validation.

    Features:
    - Automatic environment variable loading
    - Type validation and conversion
    - Default values for all settings
    - Documentation for each configuration parameter
    """
    
    # Application Settings - Basic application metadata and configuration
    app_name: str = Field(default="Document Parser API", env="APP_NAME")  # Application name for identification
    app_version: str = Field(default="1.0.0", env="APP_VERSION")  # Version number for API versioning
    debug: bool = Field(default=False, env="DEBUG")  # Enable debug mode for development

    # Server Settings - Network and server configuration
    host: str = Field(default="0.0.0.0", env="HOST")  # Host address to bind the server
    port: int = Field(default=8000, env="PORT")  # Port number for the API server
    reload: bool = Field(default=True, env="RELOAD")  # Enable auto-reload for development

    # API Settings - External service configuration
    groq_api_key: Optional[str] = Field(default=None, env="GROQ_API_KEY")  # Groq API key for LLM services

    # Processing Settings - Document processing configuration
    max_file_size_mb: int = Field(default=50, env="MAX_FILE_SIZE_MB")  # Maximum file size limit in MB
    max_batch_files: int = Field(default=50, env="MAX_BATCH_FILES")  # Maximum files in batch processing
    default_auto_detect_language: bool = Field(default=True, env="DEFAULT_AUTO_DETECT_LANGUAGE")  # Enable automatic language detection
    preserve_original_language: bool = Field(default=True, env="PRESERVE_ORIGINAL_LANGUAGE")
    
    
    cors_origins: list = Field(default=["*"], env="CORS_ORIGINS")
    cors_allow_credentials: bool = Field(default=True, env="CORS_ALLOW_CREDENTIALS")
    cors_allow_methods: list = Field(default=["*"], env="CORS_ALLOW_METHODS")
    cors_allow_headers: list = Field(default=["*"], env="CORS_ALLOW_HEADERS")
    
    
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    
    temp_dir: Optional[str] = Field(default=None, env="TEMP_DIR")
    cleanup_temp_files: bool = Field(default=True, env="CLEANUP_TEMP_FILES")
    
    
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=3600, env="RATE_LIMIT_WINDOW")  
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    def validate_required_settings(self) -> None:
        """Validate that required settings are present."""
        if not self.groq_api_key:
            print(f"DEBUG: GROQ_API_KEY from environment: {os.getenv('GROQ_API_KEY', 'NOT_FOUND')}")
            raise ValueError("GROQ_API_KEY environment variable is required")

    @property
    def max_file_size_bytes(self) -> int:
        """Convert max file size from MB to bytes."""
        return self.max_file_size_mb * 1024 * 1024

    @property
    def supported_file_extensions(self) -> set:
        """Get supported file extensions."""
        return {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.txt', '.webp', '.gif'}

    def get_temp_directory(self) -> str:
        """Get temporary directory path."""
        if self.temp_dir:
            return self.temp_dir
        return os.path.join(os.getcwd(), "temp")



settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings



try:
    settings.validate_required_settings()
except ValueError as e:
    print(f"Configuration error: {e}")
    print("Please set the GROQ_API_KEY environment variable or create a .env file")
