# UNIFIED AGENTIC DOCUMENT PROCESSING SYSTEM
# Consolidated requirements for all components

# ================================
# CORE FRAMEWORK
# ================================
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0
python-multipart>=0.0.6
aiofiles>=23.0.0

# ================================
# AUTOGEN FRAMEWORK
# ================================
pyautogen>=0.2.0

# ================================
# LLM AND AI SERVICES
# ================================
# Groq Integration
groq>=0.4.0
langchain-groq>=0.1.0

# LangChain Framework
langchain>=0.1.0
langchain-core>=0.1.0
langchain-community>=0.0.20s
instructor>=0.4.0
pymupdf>=1.23.0
pypdf2>=3.0.0
pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0
matplotlib>=3.7.0
pytesseract>=0.3.10
docling>=1.0.0
chromadb>=0.4.0
langchain-chroma>=0.1.0
sentence-transformers>=2.2.0
langchain-huggingface>=0.0.3
pandas>=2.0.0
numpy>=1.24.0
langdetect>=1.0.9
requests>=2.31.0
httpx>=0.25.0
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
structlog>=23.0.0
rich>=13.0.0
pathlib2>=2.3.7
uuid>=1.30
typing-extensions>=4.8.0
