import React from 'react';
import { useFileContext } from '../providers/FileProvider';
import { useNavigate } from 'react-router-dom';
import FilePreviewCard from './FilePreviewCard';
import GlassEffect from './GlassEffect';

const FileDownload = ({ isChatVisible }) => {
  const { outputFile } = useFileContext();
  const navigate = useNavigate();

  // Clean container classes
  const containerClass = `p-6 space-y-6 transition-all duration-300 ${
    isChatVisible ? 'max-w-[70vw]' : 'w-[95vw]'
  }`;

  // Empty state
  if (!outputFile?.length) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <GlassEffect variant="card" className="text-center max-w-md">
          <h2 className="text-2xl font-bold text-white mb-4">
            No Files Available
          </h2>
          <p className="text-gray-200 mb-6">
            Upload some files to get started with processing.
          </p>
          <GlassEffect
            variant="button"
            onClick={() => navigate('/')}
          >
            Go to Upload
          </GlassEffect>
        </GlassEffect>
      </div>
    );
  }

  return (
    <div className={containerClass}>
      {/* Header */}
      <GlassEffect variant="card" className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">
          File Processing Results
        </h1>
        <p className="text-gray-200">
          {outputFile.length} file{outputFile.length !== 1 ? 's' : ''} processed successfully
        </p>
      </GlassEffect>

      {/* File Cards */}
      <div className="space-y-6">
        {outputFile.map((fileData, index) => (
          <FilePreviewCard
            key={`${fileData.input_file?.name || 'file'}-${index}`}
            fileBlob={fileData.file}
            fileIndex={index}
            input_file={fileData.input_file}
          />
        ))}
      </div>
    </div>
  );
};

export default FileDownload;
