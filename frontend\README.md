
# 📄 Invoice Data Extractor

A modern, AI-powered invoice processing application built with React 19, featuring advanced OCR capabilities, intelligent chatbot assistance, and stunning visual effects.

---

## ✨ Features

### 🔍 **Advanced Invoice Processing**
- 📄 **OCR Technology** - Extract data from handwritten and digital invoices
- 📁 **Multiple Upload Modes** - Support for individual files and entire folders
- 🤖 **AI Chatbot** - Intelligent assistance for invoice analysis and queries
- 📊 **Data Export** - Download processed data in Excel format
- ⚡ **Real-time Processing** - Live progress tracking with extended timeout support

### 🎨 **Premium UI/UX**
- 🌊 **Fluid Animations** - Smooth popup animations and transitions throughout
- 💎 **Glass Morphism** - Modern glass effect components with backdrop blur
- 🎭 **Interactive Effects** - Water background, particle effects, and text scrambling
- 📱 **Responsive Design** - Optimized for all screen sizes
- 🎯 **Accessibility** - Enhanced focus states and keyboard navigation

### 🚀 **Performance & Polish**
- ⚡ **60fps Animations** - GPU-accelerated smooth animations
- 🔄 **Smart Caching** - Optimized file processing and state management
- 🎪 **Visual Feedback** - Toast notifications and loading states
- 🛡️ **Error Handling** - Comprehensive error management with user-friendly messages

---

### Project Structure (simplified for README)

```
arun_ui/
├── public/
│   └── index.html
├── src/
│   ├── App.jsx
│   ├── main.jsx
│   ├── index.css
│   ├── App.css
│   ├── assets/
│   ├── helpers/
│   ├── providers/
    ├── FileProvider.js
│   ├── queries/
│   │   ├── apiHelper.js
│   │   └── chatbotHelper.js
│   ├── components/
│   │   ├── FloatingChat.jsx
│   │   ├── FileUploadMain.jsx
│   │   ├── FilePreviewCard.jsx
│   │   ├── FileDownload.jsx
│   │   └── chatBotComponents/
│   │       ├── ChatMainUi.jsx
│   │       ├── ChatBubble.jsx
│   │       └── ChatInput.jsx
├── package.json
├── postcss.config.js
├── tailwind.config.js
├── vite.config.js
├── eslint.config.js
└── README.md ← (we’re going to generate this)
```

---

### Final README.md (Clean and Complete)

````markdown
# Arun UI – Floating Chatbot App

A modern React + Vite application featuring a clean, floating chatbot UI built with Tailwind CSS.

---

## ✨ Features

- ⚡ Built with **React 19** + **Vite**
- 💬 Floating chatbot UI with animated open/close
- 🎨 Styled with **Tailwind CSS**
- 🔥 Icons via `lucide-react`
- 📁 File upload & preview components
- 📦 Excel/CSV support using `xlsx` and `read-excel-file`
- 🍞 Toast notifications via `react-toastify` and `react-hot-toast`

---

## 📁 Project Structure

```bash
src/
├── components/
│   ├── FloatingChat.jsx        # Floating button + animated chat panel
│   ├── FileUploadMain.jsx      # File upload UI
│   ├── FilePreviewCard.jsx     # File preview cards
│   ├── FileDownload.jsx        # File download button
│   └── chatBotComponents/      # reused code-generation ui 
│       ├── ChatMainUi.jsx
│       ├── ChatBubble.jsx
│       └── ChatInput.jsx
├── queries/
│   ├── apiHelper.js
│   └── chatbotHelper.js
├── providers/
│   ├── FileProvider.js #  context provider

````

---

## 🚀 Getting Started

### 1. Install dependencies

```bash
npm install
```

### 2. Start development server

```bash
npm run dev
```

### 3. Build for production

```bash
npm run build
```

---

## 🛠 Tech Stack

| Tool           | Purpose                   |
| -------------- | ------------------------- |
| React 19       | Frontend UI framework     |
| Vite           | Fast dev/build tool       |
| Tailwind CSS   | Utility-first styling     |
| Lucide React   | Icon components           |
| Axios          | HTTP client for API calls |
| xlsx           | Excel/CSV handling        |
| React Toastify | Notifications             |

---

## 📦 Dependencies

```json
"react": "^19.1.0",
"vite": "^7.0.3",
"tailwindcss": "^3.4.17",
"lucide-react": "^0.525.0",
"axios": "^1.10.0",
"read-excel-file": "^5.8.8",
"xlsx": "^0.18.5"
```

---

## 🎯 Usage Guide

### **Uploading Invoices**
1. Choose upload mode (Files or Folder)
2. Select invoice files (PDF, JPG, PNG supported)
3. Wait for OCR processing (may take several minutes for handwritten invoices)
4. View extracted data and download results

### **Using the Chatbot**
1. Click the floating chat button
2. Ask questions about your processed invoices
3. Use suggested queries or type custom questions
4. Get instant AI-powered responses

### **Keyboard Shortcuts**
- `Enter` - Send chat message
- `Shift + Enter` - New line in chat input
- `Escape` - Close chatbot

---

## 🎨 Customization

### **Animation Timing**
Modify animation delays in components:
```jsx
<GlassEffect
  popup={true}
  popupDelay="0.3s"  // Custom delay
  className="..."
>
```

### **Glass Effect Variants**
```jsx
<GlassEffect variant="button" />  // Button styling
<GlassEffect variant="card" />    // Card styling
<GlassEffect variant="input" />   // Input styling
```

---

## 🚀 Performance

### **Optimization Features**
- ⚡ **Code Splitting** - Lazy loading of components
- 🎯 **Tree Shaking** - Unused code elimination
- 💾 **Asset Optimization** - Compressed images and fonts
- 🔄 **Caching** - Efficient browser caching strategies

### **Animation Performance**
- 🎮 **GPU Acceleration** - Hardware-accelerated animations
- 📊 **60fps Target** - Smooth 60 frames per second
- 🎛️ **will-change** - Optimized CSS properties
- ⚡ **RequestAnimationFrame** - Efficient animation loops

---
