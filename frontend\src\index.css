@tailwind base;
@tailwind components;
@tailwind utilities;


:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light ; 

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0; 
  min-width: 320px;
  min-height: 100vh;
  height:100vh;
  width:100vw;
   color: #000;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* @media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
} */

/* Animated Ripples Background */
.ripple-background {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 400% 400%;
  animation: gradientShift 20s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: ripple 18s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
  box-shadow: 0px 0px 1px 0px rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(1px);
  will-change: transform, opacity;
}

.small {
  width: 200px;
  height: 200px;
  left: -100px;
  bottom: -100px;
}

.medium {
  width: 400px;
  height: 400px;
  left: -200px;
  bottom: -200px;
}

.large {
  width: 600px;
  height: 600px;
  left: -300px;
  bottom: -300px;
}

.xlarge {
  width: 800px;
  height: 800px;
  left: -400px;
  bottom: -400px;
}

.xxlarge {
  width: 1000px;
  height: 1000px;
  left: -500px;
  bottom: -500px;
}

.shade1 {
  opacity: 0.2;
  animation-delay: 0s;
}

.shade2 {
  opacity: 0.5;
  animation-delay: -3s;
}

.shade3 {
  opacity: 0.7;
  animation-delay: -6s;
}

.shade4 {
  opacity: 0.8;
  animation-delay: -9s;
}

.shade5 {
  opacity: 0.9;
  animation-delay: -12s;
}

@keyframes ripple {
  0% {
    transform: scale(0.8) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: scale(1.0) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: scale(1.0) rotate(270deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8) rotate(360deg);
    opacity: 0.6;
  }
}

/* Additional circle positions */
.top-right {
  left: auto !important;
  bottom: auto !important;
  right: -100px;
  top: -100px;
  animation-direction: reverse;
}

.center-float {
  left: 50% !important;
  bottom: auto !important;
  top: 30%;
  transform: translateX(-50%);
  animation-delay: -7s;
}

.center-float-2 {
  left: 20% !important;
  bottom: auto !important;
  top: 60%;
  animation-delay: -10s;
  animation-direction: reverse;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Clean Glass Effect System */
.glass-filter {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  pointer-events: none;
}

.glass {
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(16px) saturate(180%);
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  will-change: transform, box-shadow, background;
}

.glass-bg {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  filter: url(#glass-blur);
}

.glass-content {
  position: relative;
  z-index: 2;
}

/* Button Variant */
.glass-btn {
  border-radius: 16px;
  padding: 12px 24px;
  cursor: pointer;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(12px) saturate(180%);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, box-shadow, background, border-color;
}

.glass-btn:hover {
  transform: translateY(-3px) scale(1.03);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.18),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.6);
}

.glass-btn:active {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Card Variant */
.glass-card {
  border-radius: 24px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(16px) saturate(180%);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, box-shadow, background;
}

.glass-card:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.18),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
}

/* Input Variant */
.glass-input {
  border-radius: 8px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* Disabled State */
.glass-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Responsive */
@media (max-width: 768px) {
  .glass {
    backdrop-filter: blur(8px);
  }

  .glass-btn {
    padding: 10px 20px;
  }

  .glass-card {
    padding: 16px;
    border-radius: 16px;
  }
}

/* Custom animations for upload page */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-5px) rotate(1deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-12px) rotate(0deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-5px) rotate(-1deg);
    opacity: 0.9;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  50% {
    opacity: 0.7;
    transform: translateY(15px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-float {
  animation: float 4s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
  will-change: transform, opacity;
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Text Scramble Effect Styles */
.text-scramble {
  font-family: 'Roboto Mono', 'Courier New', monospace;
  font-weight: 300;
  color: #FAFAFA;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.text-scramble-dud {
  color: #757575;
  opacity: 0.7;
}

/* Water Background Animation Styles */
.water-background {
  background: linear-gradient(to bottom, #0077be, #003366);
  perspective: 1000px;
}

.water-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  opacity: 0.6;
  will-change: transform, opacity;
}

.layer-1 {
  background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.03) 0%, rgba(0, 0, 0, 0) 50%),
              radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.02) 0%, rgba(0, 0, 0, 0) 60%);
  background-size: 200px;
  animation: wave-motion-1 25s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite alternate;
  filter: blur(8px);
  opacity: 0.5;
}

.layer-2 {
  background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, rgba(0, 0, 0, 0) 60%);
  animation: wave-motion-2 22s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
  filter: blur(5px);
  opacity: 0.7;
  transform: translateZ(-50px);
}

.layer-3 {
  background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
  animation: wave-motion-3 28s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite alternate-reverse;
  filter: blur(6px);
  opacity: 0.6;
  transform: translateZ(-100px);
}

.water-highlights {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at var(--highlight-x, 50%) var(--highlight-y, 50%),
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0) 50%
  );
  mix-blend-mode: screen;
  opacity: var(--highlight-opacity, 0.7);
  will-change: transform, opacity;
}

.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.water-particle {
  position: absolute;
  width: 5px;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  pointer-events: none;
  animation: particle-fade-out 1s ease-out forwards;
  will-change: transform, opacity;
}

/* Water animation keyframes */
@keyframes wave-motion-1 {
  0% {
    transform: scale(1, 1) translate(0, 0) rotate(0deg);
    opacity: 0.5;
  }
  25% {
    transform: scale(1.02, 0.98) translate(15px, 8px) rotate(0.5deg);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.04, 0.96) translate(30px, 15px) rotate(1deg);
    opacity: 0.7;
  }
  75% {
    transform: scale(1.02, 0.98) translate(15px, 8px) rotate(0.5deg);
    opacity: 0.6;
  }
  100% {
    transform: scale(1, 1) translate(0, 0) rotate(0deg);
    opacity: 0.5;
  }
}

@keyframes wave-motion-2 {
  0% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.7;
  }
  33% {
    transform: translate(20px, 10px) rotate(1deg);
    opacity: 0.8;
  }
  66% {
    transform: translate(40px, 15px) rotate(1.5deg);
    opacity: 0.9;
  }
  100% {
    transform: translate(50px, 20px) rotate(2deg);
    opacity: 0.7;
  }
}

@keyframes wave-motion-3 {
  0% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translate(-15px, -8px) rotate(-0.5deg);
    opacity: 0.7;
  }
  50% {
    transform: translate(-40px, -15px) rotate(-1deg);
    opacity: 0.8;
  }
  75% {
    transform: translate(-20px, -8px) rotate(-0.5deg);
    opacity: 0.7;
  }
  100% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.6;
  }
}

@keyframes particle-fade-out {
  0% {
    transform: translate(0, 0) scale(1) rotate(0deg);
    opacity: 1;
  }
  25% {
    transform: translate(calc(var(--particle-end-x) * 0.3), calc(var(--particle-end-y) * 0.3)) scale(0.9) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translate(calc(var(--particle-end-x) * 0.6), calc(var(--particle-end-y) * 0.6)) scale(0.6) rotate(180deg);
    opacity: 0.5;
  }
  75% {
    transform: translate(calc(var(--particle-end-x) * 0.8), calc(var(--particle-end-y) * 0.8)) scale(0.3) rotate(270deg);
    opacity: 0.2;
  }
  100% {
    transform: translate(var(--particle-end-x), var(--particle-end-y)) scale(0.1) rotate(360deg);
    opacity: 0;
  }
}

/* Enhanced Smooth Animation Classes */
.smooth-hover {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, box-shadow, background;
}

.smooth-hover:hover {
  transform: translateY(-2px) scale(1.02);
  filter: brightness(1.1);
}

.ultra-smooth {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity, filter;
}

.bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3) rotate(-10deg);
    opacity: 0;
  }
  25% {
    transform: scale(1.05) rotate(2deg);
    opacity: 0.7;
  }
  50% {
    transform: scale(0.95) rotate(-1deg);
    opacity: 0.9;
  }
  75% {
    transform: scale(1.02) rotate(0.5deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* Smooth Pulse Animation */
.smooth-pulse {
  animation: smoothPulse 3s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
}

@keyframes smoothPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Fluid Rotation */
.fluid-rotate {
  animation: fluidRotate 20s linear infinite;
}

@keyframes fluidRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Particle Effect Styles */
.particle-canvas {
  mix-blend-mode: screen;
  opacity: 0.8;
}

.particle-effect-container {
  overflow: hidden;
}

/* Fluid Popup Animations */
.popup-enter {
  animation: popupEnter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.popup-exit {
  animation: popupExit 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes popupEnter {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px) rotate(-2deg);
    filter: blur(4px);
  }
  30% {
    opacity: 0.6;
    transform: scale(0.95) translateY(10px) rotate(-1deg);
    filter: blur(2px);
  }
  60% {
    opacity: 0.9;
    transform: scale(1.02) translateY(-2px) rotate(0.5deg);
    filter: blur(0px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0) rotate(0deg);
    filter: blur(0px);
  }
}

@keyframes popupExit {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0) rotate(0deg);
    filter: blur(0px);
  }
  100% {
    opacity: 0;
    transform: scale(0.9) translateY(-10px) rotate(1deg);
    filter: blur(2px);
  }
}

/* Modal Backdrop Animation */
.modal-backdrop-enter {
  animation: backdropEnter 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.modal-backdrop-exit {
  animation: backdropExit 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes backdropEnter {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

@keyframes backdropExit {
  0% {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
  100% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
}

/* Error Message Popup */
.error-popup {
  animation: errorPopup 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes errorPopup {
  0% {
    opacity: 0;
    transform: translateX(-100%) scale(0.9);
  }
  50% {
    opacity: 0.8;
    transform: translateX(10px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Success Message Popup */
.success-popup {
  animation: successPopup 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes successPopup {
  0% {
    opacity: 0;
    transform: translateY(-50px) scale(0.8) rotate(-5deg);
  }
  40% {
    opacity: 0.7;
    transform: translateY(5px) scale(1.05) rotate(2deg);
  }
  70% {
    opacity: 0.9;
    transform: translateY(-2px) scale(0.98) rotate(-1deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotate(0deg);
  }
}

/* Tooltip Popup */
.tooltip-popup {
  animation: tooltipPopup 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes tooltipPopup {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Button Press Animation */
.button-press {
  animation: buttonPress 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* Staggered Popup Animation */
.stagger-popup {
  animation: staggerPopup 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes staggerPopup {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9) rotate(-1deg);
  }
  40% {
    opacity: 0.6;
    transform: translateY(10px) scale(0.95) rotate(0.5deg);
  }
  70% {
    opacity: 0.9;
    transform: translateY(-5px) scale(1.02) rotate(-0.2deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotate(0deg);
  }
}

/* Slide In From Left */
.slide-in-left {
  animation: slideInLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-100px) scale(0.9);
  }
  60% {
    opacity: 0.8;
    transform: translateX(10px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Slide In From Right */
.slide-in-right {
  animation: slideInRight 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(100px) scale(0.9);
  }
  60% {
    opacity: 0.8;
    transform: translateX(-10px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Gentle Bounce */
.gentle-bounce {
  animation: gentleBounce 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes gentleBounce {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  30% {
    opacity: 0.7;
    transform: translateY(5px) scale(1.02);
  }
  60% {
    opacity: 0.9;
    transform: translateY(-2px) scale(0.99);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Fade Scale In */
.fade-scale-in {
  animation: fadeScaleIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes fadeScaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
