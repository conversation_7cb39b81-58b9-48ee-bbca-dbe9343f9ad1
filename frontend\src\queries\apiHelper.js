import axios from 'axios';
import * as XLSX from 'xlsx';

const standardApi = 'http://localhost:8000/';

// Check if backend server is available
const checkBackendHealth = async () => {
  console.log('🔍 Testing backend connection to:', standardApi);

  try {
    console.log('📡 Trying health endpoint...');
    const response = await axios.get(`${standardApi}health`, { timeout: 5000 });
    console.log('✅ Health endpoint responded:', response.status);
    return true;
  } catch (error) {
    console.log('❌ Health endpoint failed:', error.message);

    try {
      console.log('📡 Trying root endpoint...');
      const response = await axios.get(`${standardApi}`, { timeout: 5000 });
      console.log('✅ Root endpoint responded:', response.status);
      return true;
    } catch (error2) {
      console.log('❌ Root endpoint failed:', error2.message);
      console.log('🔴 Backend server not responding at', standardApi);
      return false;
    }
  }
};

// Fallback function to process Excel files locally
const processExcelLocally = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const workbook = XLSX.read(e.target.result, { type: 'array' });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(sheet);

        // Convert back to CSV blob
        const csvData = XLSX.utils.sheet_to_csv(sheet);
        const blob = new Blob([csvData], { type: 'text/csv' });

        console.log('✅ Local Excel processing successful:', jsonData.length, 'rows');
        resolve(blob);
      } catch (err) {
        console.error('❌ Local Excel processing failed:', err);
        reject(err);
      }
    };

    reader.onerror = () => reject(new Error('Failed to read Excel file'));
    reader.readAsArrayBuffer(file);
  });
};

// Fallback function to process text files locally
const processTextLocally = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const textContent = e.target.result;

        // Create CSV with text analysis
        const lines = textContent.split('\n').filter(line => line.trim());
        const wordCount = textContent.split(/\s+/).filter(word => word.trim()).length;
        const charCount = textContent.length;

        const csvContent = `Metric,Value\n` +
                          `"File Name","${file.name}"\n` +
                          `"File Size","${(file.size / 1024).toFixed(1)} KB"\n` +
                          `"Line Count","${lines.length}"\n` +
                          `"Word Count","${wordCount}"\n` +
                          `"Character Count","${charCount}"\n` +
                          `"First 100 Characters","${textContent.substring(0, 100).replace(/"/g, '""')}"`;

        const blob = new Blob([csvContent], { type: 'text/csv' });

        console.log('✅ Local text processing successful:', lines.length, 'lines');
        resolve(blob);
      } catch (err) {
        console.error('❌ Local text processing failed:', err);
        reject(err);
      }
    };

    reader.onerror = () => reject(new Error('Failed to read text file'));
    reader.readAsText(file);
  });
};

const uploadFile = async (file) => {
  console.log('🚀 Starting file upload:', file.name, 'Size:', (file.size / 1024).toFixed(1), 'KB');
  console.log('📋 File details:', {
    name: file.name,
    type: file.type,
    size: file.size,
    lastModified: new Date(file.lastModified).toISOString()
  });

  // Always try backend first - no health check to avoid false negatives
  console.log('📡 Attempting direct backend connection to:', standardApi + 'process-document/csv');

  // Determine timeout based on file type and size
  const isPdf = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
  const isImageFile = file.type.startsWith('image/') || /\.(jpg|jpeg|png|gif|bmp|tiff|webp)$/i.test(file.name);
  const isHandwrittenFile = file.name.toLowerCase().includes('handwritten') ||
                           file.name.toLowerCase().includes('hand') ||
                           isImageFile; // Treat all images as potentially handwritten
  const isLargeFile = file.size > 5 * 1024 * 1024; // 5MB

  // Extended timeouts for complex processing
  let timeout;
  if (isHandwrittenFile || isImageFile) {
    timeout = 300000; // 5 minutes for handwritten/image files (OCR takes time)
  } else if (isPdf) {
    timeout = 180000; // 3 minutes for PDFs
  } else if (isLargeFile) {
    timeout = 120000; // 2 minutes for large files
  } else {
    timeout = 60000;  // 1 minute for normal files (increased from 30s)
  }

  const fileTypeDescription = isHandwrittenFile ? 'handwritten/image' :
                             isPdf ? 'PDF' :
                             isLargeFile ? 'large' : 'normal';

  console.log(`⏰ Using ${timeout/1000}s timeout for ${fileTypeDescription} file (${file.name})`);
  console.log(`📋 File analysis: handwritten=${isHandwrittenFile}, image=${isImageFile}, PDF=${isPdf}, large=${isLargeFile}`);

  try {
    const formData = new FormData();
    formData.append('file', file);

    console.log('📤 Sending request to backend...');
    console.log('🔗 URL:', standardApi + 'process-document/csv');
    console.log('📦 FormData contains:', file.name);

    const response = await axios.post(`${standardApi}process-document/csv`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      responseType: 'blob',
      timeout: timeout,
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        console.log(`📤 Upload progress: ${percentCompleted}%`);
      }
    });

    console.log('✅ Backend API SUCCESS!');
    console.log('📊 Response details:', {
      status: response.status,
      statusText: response.statusText,
      dataSize: response.data.size,
      dataType: response.data.type,
      headers: response.headers
    });

    // Validate the response data
    if (!response.data) {
      console.log('⚠️ Backend returned empty response data');
      throw new Error('Backend returned empty response');
    }

    if (response.data instanceof Blob && response.data.size === 0) {
      console.log('⚠️ Backend returned empty Blob');
      throw new Error('Backend returned empty file');
    }

    console.log('✅ Valid response data received, size:', response.data.size, 'bytes');
    return response.data;

  } catch (error) {
    console.log('❌ Backend API failed:', error.message);

    // Check if it's a timeout vs server error
    const isTimeout = error.message.includes('timeout');
    const isNetworkError = error.code === 'ECONNREFUSED' || error.message.includes('Network Error');

    if (isTimeout) {
      console.log('⏰ Request timed out - file processing taking too long');

      // Check if it's a handwritten/image file that timed out
      const isImageFile = file.type.startsWith('image/') || /\.(jpg|jpeg|png|gif|bmp|tiff|webp)$/i.test(file.name);
      const isHandwrittenFile = file.name.toLowerCase().includes('handwritten') ||
                               file.name.toLowerCase().includes('hand') ||
                               isImageFile;

      if (isHandwrittenFile) {
        console.log('🖼️ Handwritten/Image file timed out - OCR processing can take longer');
        throw new Error(`Handwritten file processing timed out for ${file.name}. OCR and text extraction can take several minutes. The backend may still be processing - please wait and try again in a few minutes.`);
      } else {
        throw new Error(`Backend processing timed out for ${file.name}. This file may require more processing time. Please try again or contact support.`);
      }
    }

    if (isNetworkError) {
      console.log('🔌 Network error - backend server not reachable');
    }

    // Check file type for fallback processing
    const isExcelFile = file.type.includes('spreadsheet') ||
                       file.name.match(/\.(xlsx?|csv)$/i);
    const isPdfFile = file.type === 'application/pdf' ||
                     file.name.toLowerCase().endsWith('.pdf');
    const isImageFile = file.type.startsWith('image/');
    const isTextFile = file.type.startsWith('text/') ||
                      file.name.match(/\.(txt|json|xml)$/i);

    if (isExcelFile) {
      console.log('🔄 Falling back to local Excel processing...');
      try {
        const result = await processExcelLocally(file);
        console.log('✅ Local Excel processing successful');
        return result;
      } catch (localError) {
        console.error('❌ Local Excel processing failed:', localError);
        throw new Error(`Excel processing failed: ${localError.message}`);
      }
    } else if (isPdfFile) {
      console.log('📄 PDF file detected - backend required for content extraction');

      const errorReason = isTimeout ? 'Backend processing timed out' :
                         isNetworkError ? 'Backend server not available' :
                         'Backend API error';

      // For PDFs, create an informative CSV about the processing attempt
      const csvContent = `File Name,File Type,File Size,Status,Issue,Solution\n` +
                        `"${file.name}","PDF","${(file.size / 1024).toFixed(1)} KB","Failed","${errorReason}","Start backend server at localhost:8000 for PDF content extraction"`;
      const blob = new Blob([csvContent], { type: 'text/csv' });
      console.log('⚠️ PDF processing failed - created info CSV');
      return blob;
    } else if (isImageFile) {
      console.log('🖼️ Image file detected - creating placeholder CSV...');
      const csvContent = `File Name,File Type,File Size,Status,Note\n"${file.name}","Image","${(file.size / 1024).toFixed(1)} KB","Processed","Image analysis requires backend server"`;
      const blob = new Blob([csvContent], { type: 'text/csv' });
      console.log('✅ Image placeholder created');
      return blob;
    } else if (isTextFile) {
      console.log('📝 Text file detected - processing locally...');
      try {
        const result = await processTextLocally(file);
        console.log('✅ Local text processing successful');
        return result;
      } catch (localError) {
        console.error('❌ Local text processing failed:', localError);
        throw new Error(`Text processing failed: ${localError.message}`);
      }
    } else {
      console.log('❓ Unknown file type - creating basic info CSV...');
      const csvContent = `File Name,File Type,File Size,Status,Note\n"${file.name}","${file.type || 'Unknown'}","${(file.size / 1024).toFixed(1)} KB","Processed","File type requires backend server for processing"`;
      const blob = new Blob([csvContent], { type: 'text/csv' });
      console.log('✅ Basic info CSV created');
      return blob;
    }
  }
};

// Manual backend test function - call this from browser console
const testBackend = async () => {
  console.log('🧪 Manual Backend Test Starting...');
  console.log('🔗 Testing URL:', standardApi);

  try {
    // Test 1: Basic connectivity
    console.log('📡 Test 1: Basic connectivity...');
    const basicTest = await axios.get(standardApi, { timeout: 10000 });
    console.log('✅ Basic connectivity OK:', basicTest.status);
  } catch (error) {
    console.log('❌ Basic connectivity failed:', error.message);
  }

  try {
    // Test 2: Health endpoint
    console.log('📡 Test 2: Health endpoint...');
    const healthTest = await axios.get(standardApi + 'health', { timeout: 10000 });
    console.log('✅ Health endpoint OK:', healthTest.status);
  } catch (error) {
    console.log('❌ Health endpoint failed:', error.message);
  }

  try {
    // Test 3: API endpoint (GET)
    console.log('📡 Test 3: API endpoint (GET)...');
    const apiTest = await axios.get(standardApi + 'process-document/csv', { timeout: 10000 });
    console.log('✅ API endpoint OK:', apiTest.status);
  } catch (error) {
    console.log('❌ API endpoint failed:', error.message);
  }

  console.log('🧪 Manual Backend Test Complete');
  console.log('💡 If all tests failed, your backend server is not running on localhost:8000');
  console.log('💡 If some tests passed, your backend is running but may have different endpoints');
};

// Make testBackend available globally for console testing
if (typeof window !== 'undefined') {
  window.testBackend = testBackend;
}

export { uploadFile, testBackend };