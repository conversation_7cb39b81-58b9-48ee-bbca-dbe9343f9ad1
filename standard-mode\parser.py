import os
import time
import logging
import csv
from pathlib import Path
from typing import Optional, Union, List
import json
import re



import pytesseract
from PIL import Image
import fitz


from groq import Groq
import instructor


try:
    from langdetect import detect
    LANGDETECT_AVAILABLE = True
except ImportError:
    LANGDETECT_AVAILABLE = False



from models import InvoiceData, DocumentParsingResult, LineItem


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DocumentParser:

    def __init__(self, groq_api_key: Optional[str] = None, auto_detect_language: bool = True, preserve_original_language: bool = True):
        self.groq_api_key = groq_api_key or os.getenv("GROQ_API_KEY")
        if not self.groq_api_key:
            raise ValueError("GROQ_API_KEY environment variable or parameter required")


        self.groq_client = Groq(api_key=self.groq_api_key)
        self.client = instructor.from_groq(self.groq_client)


        self.auto_detect_language = auto_detect_language
        self.preserve_original_language = preserve_original_language

        if not LANGDETECT_AVAILABLE and auto_detect_language:
            logger.warning("langdetect not available - language detection will be limited")

        self.supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.txt', '.webp', '.gif'}
        self.language_mappings = {
            'en': 'eng',      
            'es': 'spa',      
            'fr': 'fra',      
            'de': 'deu',      
            'it': 'ita',      
            'pt': 'por',      
            'ru': 'rus',      
            'zh': 'chi_sim+chi_tra',  
            'ja': 'jpn',      
            'ko': 'kor',      
            'ar': 'ara',      
            'hi': 'hin',      
            'th': 'tha',      
            'vi': 'vie',      
            'tr': 'tur',      
            'pl': 'pol',      
            'nl': 'nld',      
            'sv': 'swe',      
            'da': 'dan',      
            'no': 'nor',      
            'fi': 'fin',      
            'cs': 'ces',      
            'sk': 'slk',      
            'hu': 'hun',      
            'ro': 'ron',      
            'bg': 'bul',      
            'hr': 'hrv',      
            'sr': 'srp',      
            'sl': 'slv',      
            'et': 'est',      
            'lv': 'lav',      
            'lt': 'lit',      
            'uk': 'ukr',      
            'be': 'bel',      
            'mk': 'mkd',      
            'sq': 'sqi',      
            'eu': 'eus',      
            'ca': 'cat',      
            'gl': 'glg',      
            'cy': 'cym',      
            'ga': 'gle',      
            'mt': 'mlt',      
            'is': 'isl',      
            'fo': 'fao',      
            'he': 'heb',      
            'fa': 'fas',      
            'ur': 'urd',      
            'bn': 'ben',      
            'gu': 'guj',      
            'pa': 'pan',      
            'ta': 'tam',      
            'te': 'tel',      
            'kn': 'kan',      
            'ml': 'mal',      
            'si': 'sin',      
            'my': 'mya',      
            'km': 'khm',      
            'lo': 'lao',      
            'ka': 'kat',      
            'hy': 'hye',      
            'az': 'aze',      
            'kk': 'kaz',      
            'ky': 'kir',      
            'uz': 'uzb',      
            'tg': 'tgk',      
            'mn': 'mon',      
            'ne': 'nep',      
            'mr': 'mar',      
            'or': 'ori',      
            'as': 'asm',      
            'sd': 'snd',      
            'ps': 'pus',      
            'ku': 'kur',      
            'am': 'amh',      
            'ti': 'tir',      
            'om': 'orm',      
            'so': 'som',      
            'sw': 'swa',      
            'zu': 'zul',      
            'xh': 'xho',      
            'af': 'afr',      
            'yo': 'yor',      
            'ig': 'ibo',      
            'ha': 'hau',      
            'ms': 'msa',      
            'id': 'ind',      
            'tl': 'fil',      
            'ceb': 'ceb',     
            'jv': 'jav',      
            'su': 'sun',      
            'mg': 'mlg',      
            'ny': 'nya',      
            'sn': 'sna',      
            'st': 'sot',      
            'tn': 'tsn',      
            've': 'ven',      
            'ts': 'tso',      
            'ss': 'ssw',      
            'nr': 'nbl',      
        }

        self.default_ocr_languages = 'eng+spa+fra+deu+ita+por+rus+chi_sim+jpn+kor+ara+hin'

    def detect_language(self, text: str) -> str:
        """Detect the language of the given text."""
        if not text or not text.strip():
            return 'en'  

        if LANGDETECT_AVAILABLE:
            try:
                detected_lang = detect(text)
                logger.info(f"Detected language: {detected_lang}")
                return detected_lang
            except Exception as e:
                logger.warning(f"Language detection failed: {e}")

        return self._heuristic_language_detection(text)

    def _heuristic_language_detection(self, text: str) -> str:
        """Simple heuristic-based language detection as fallback."""
        text_lower = text.lower()

 
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            return 'zh'  
        elif any('\u3040' <= char <= '\u309f' or '\u30a0' <= char <= '\u30ff' for char in text):
            return 'ja'  
        elif any('\uac00' <= char <= '\ud7af' for char in text):
            return 'ko'  
        elif any('\u0600' <= char <= '\u06ff' for char in text):
            return 'ar'  
        elif any('\u0590' <= char <= '\u05ff' for char in text):
            return 'he'  
        elif any('\u0400' <= char <= '\u04ff' for char in text):
            return 'ru'  
        elif any('\u0900' <= char <= '\u097f' for char in text):
            return 'hi'  
        elif any('\u0e00' <= char <= '\u0e7f' for char in text):
            return 'th'  


        spanish_indicators = ['el', 'la', 'de', 'que', 'y', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'una', 'del', 'las', 'los']
        french_indicators = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 'par', 'grand', 'en', 'une', 'être', 'et', 'à', 'il', 'avoir', 'ne', 'je', 'son', 'que', 'se', 'qui', 'ce', 'dans', 'en', 'du', 'elle', 'au', 'de', 'le', 'tout', 'et', 'je', 'par', 'sur', 'faire', 'plus', 'dire', 'me', 'on', 'mon', 'lui', 'nous', 'comme', 'mais', 'pouvoir', 'avec', 'tu', 'y', 'aller', 'voir', 'en', 'bon', 'savoir', 'notre', 'faire', 'le', 'ça', 'chaque', 'là']
        german_indicators = ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich', 'des', 'auf', 'für', 'ist', 'im', 'dem', 'nicht', 'ein', 'eine', 'als', 'auch', 'es', 'an', 'werden', 'aus', 'er', 'hat', 'dass', 'sie', 'nach', 'wird', 'bei', 'einer', 'um', 'am', 'sind', 'noch', 'wie', 'einem', 'über', 'einen', 'so', 'zum', 'war', 'haben', 'nur', 'oder', 'aber', 'vor', 'zur', 'bis', 'mehr', 'durch', 'man', 'sein', 'wurde', 'sei', 'in']
        italian_indicators = ['il', 'di', 'che', 'e', 'la', 'per', 'un', 'in', 'con', 'del', 'da', 'a', 'al', 'le', 'si', 'dei', 'come', 'io', 'lo', 'tutto', 'ma', 'dalla', 'te', 'le', 'alla', 'nel', 'della', 'per', 'una', 'su', 'anche', 'come', 'dopo', 'senza', 'sopra', 'può', 'dove', 'chi', 'molto', 'fare', 'ecco', 'alcuni', 'bene', 'quale', 'cosa', 'ogni', 'questo', 'bello', 'quello', 'lui', 'tempo', 'hai', 'nel', 'sono', 'cose', 'così', 'ora', 'quando', 'anche', 'ci', 'io', 'questo', 'qui', 'tutto', 'ancora', 'altri', 'molto', 'uno', 'me', 'solo', 'sì', 'mai', 'casa', 'oggi', 'allora', 'felice', 'essere', 'molta', 'stato', 'subito', 'place']
        portuguese_indicators = ['o', 'de', 'a', 'e', 'do', 'da', 'em', 'um', 'para', 'é', 'com', 'não', 'uma', 'os', 'no', 'se', 'na', 'por', 'mais', 'as', 'dos', 'como', 'mas', 'foi', 'ao', 'ele', 'das', 'tem', 'à', 'seu', 'sua', 'ou', 'ser', 'quando', 'muito', 'há', 'nos', 'já', 'está', 'eu', 'também', 'só', 'pelo', 'pela', 'até', 'isso', 'ela', 'entre', 'era', 'depois', 'sem', 'mesmo', 'aos', 'ter', 'seus', 'suas', 'numa', 'pelos', 'pelas', 'esse', 'eles', 'estão', 'você', 'tinha', 'foram', 'essa', 'num', 'nem', 'suas', 'meu', 'às', 'minha', 'têm', 'numa', 'pelos', 'pelas', 'essas', 'esses', 'pelas', 'pelos', 'à', 'às', 'essa', 'essas', 'isso', 'isto']

        words = text_lower.split()
        if len(words) < 3:
            return 'en'  

        
        spanish_count = sum(1 for word in words if word in spanish_indicators)
        french_count = sum(1 for word in words if word in french_indicators)
        german_count = sum(1 for word in words if word in german_indicators)
        italian_count = sum(1 for word in words if word in italian_indicators)
        portuguese_count = sum(1 for word in words if word in portuguese_indicators)

        counts = {
            'es': spanish_count,
            'fr': french_count,
            'de': german_count,
            'it': italian_count,
            'pt': portuguese_count
        }

        max_lang = max(counts, key=counts.get)
        if counts[max_lang] > len(words) * 0.1:  
            return max_lang

        return 'en'  

    def get_tesseract_language_config(self, detected_language: str) -> str:
        """Get appropriate Tesseract language configuration based on detected language."""
        if detected_language in self.language_mappings:
            tesseract_lang = self.language_mappings[detected_language]
            logger.info(f"Using Tesseract language: {tesseract_lang} for detected language: {detected_language}")
            return tesseract_lang
        else:
            logger.info(f"Language {detected_language} not in mapping, using default multi-language config")
            return self.default_ocr_languages

    def detect_document_type(self, file_path: Path) -> str:
        extension = file_path.suffix.lower()

        if extension == '.pdf':
            return 'pdf'
        elif extension in {'.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.webp', '.gif'}:
            return 'image'
        elif extension == '.txt':
            return 'text'
        else:
            raise ValueError(f"Unsupported file type: {extension}")
    
    def extract_text_from_pdf(self, file_path: Path, language_hint: Optional[str] = None) -> str:
        try:
            doc = fitz.open(file_path)
            text = ""

            for page_num in range(doc.page_count):
                page = doc[page_num]
                text += page.get_text()

            doc.close()

            if text.strip() and self.auto_detect_language and not language_hint:
                detected_lang = self.detect_language(text)
                logger.info(f"Detected language in PDF text: {detected_lang}")

            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")

            return self.extract_text_with_ocr(file_path, language_hint)
    




    def _check_tesseract_available(self) -> bool:
        """Check if Tesseract OCR is available."""
        try:
            import pytesseract
            pytesseract.get_tesseract_version()
            return True
        except Exception:
            return False





    def extract_text_with_ocr(self, file_path: Path, language_hint: Optional[str] = None) -> str:
        """Extract text using OCR with multi-language support."""
        try:
            if file_path.suffix.lower() == '.pdf':
                doc = fitz.open(file_path)
                text = ""

                for page_num in range(doc.page_count):
                    page = doc[page_num]
                    pix = page.get_pixmap(matrix=fitz.Matrix(2, 2)) 
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                    
                    page_text = self._extract_text_from_image_multilang(img, language_hint)
                    text += page_text + "\n"

                doc.close()
                return text.strip()
            else:
                img = Image.open(file_path)
                text = self._extract_text_from_image_multilang(img, language_hint)
                return text.strip()

        except Exception as e:
            logger.error(f"Error during OCR: {e}")
            return ""

    def _extract_text_from_image_multilang(self, img: Image.Image, language_hint: Optional[str] = None) -> str:
        """Extract text from image with automatic language detection and appropriate OCR configuration."""
        try:
            if language_hint:
                tesseract_lang = self.get_tesseract_language_config(language_hint)
            else:
                tesseract_lang = self.default_ocr_languages

           
            config = f'--oem 3 --psm 6 -l {tesseract_lang}'
            text = pytesseract.image_to_string(img, config=config)

            if text.strip():
                if self.auto_detect_language and not language_hint:
                    detected_lang = self.detect_language(text)
                    if detected_lang != 'en':  
                        specific_lang = self.get_tesseract_language_config(detected_lang)
                        if specific_lang != tesseract_lang:
                            logger.info(f"Re-running OCR with detected language: {detected_lang}")
                            config_specific = f'--oem 3 --psm 6 -l {specific_lang}'
                            text_specific = pytesseract.image_to_string(img, config=config_specific)
                            if len(text_specific.strip()) > len(text.strip()):
                                text = text_specific

                return text.strip()

            fallback_configs = [
                f'--oem 3 --psm 7 -l {tesseract_lang}',  # Single text line
                f'--oem 3 --psm 8 -l {tesseract_lang}',  # Single word
                f'--oem 3 --psm 11 -l {tesseract_lang}', # Sparse text
                f'--oem 3 --psm 13 -l {tesseract_lang}', # Raw line
            ]

            for config in fallback_configs:
                try:
                    text = pytesseract.image_to_string(img, config=config)
                    if text.strip():
                        logger.info(f"Fallback OCR successful with config: {config}")
                        return text.strip()
                except Exception as e:
                    logger.debug(f"Fallback config failed: {config}, error: {e}")
                    continue

            return ""

        except Exception as e:
            logger.error(f"Multi-language OCR failed: {e}")
            return ""
    
    def extract_text(self, file_path: Path, language_hint: Optional[str] = None) -> str:
        """Extract text from document based on type with multi-language support."""
        doc_type = self.detect_document_type(file_path)

        if doc_type == 'text':
            text = file_path.read_text(encoding='utf-8')
            if self.auto_detect_language and not language_hint:
                detected_lang = self.detect_language(text)
                logger.info(f"Detected language in text file: {detected_lang}")
            return text
        elif doc_type == 'pdf':
            return self.extract_text_from_pdf(file_path, language_hint)
        elif doc_type == 'image':
            return self.extract_text_with_ocr(file_path, language_hint)
        else:
            raise ValueError(f"Unsupported document type: {doc_type}")
    
    def create_extraction_prompt(self, text: str, detected_language: Optional[str] = None) -> str:
        if not detected_language and self.auto_detect_language:
            detected_language = self.detect_language(text)

        language_note = ""
        if detected_language and detected_language != 'en':
            if self.preserve_original_language:
                language_note = f"""
LANGUAGE PROCESSING NOTE:
- The document appears to be in {detected_language.upper()}
- Extract all information in the ORIGINAL LANGUAGE ({detected_language.upper()})
- Keep field names and values in their original language
- Preserve original text formatting and language
- Handle currency symbols and number formats appropriate to the language/region
- Only use English for the JSON field names (keys), but keep values in original language
"""
            else:
                language_note = f"""
LANGUAGE PROCESSING NOTE:
- The document appears to be in {detected_language.upper()}
- Extract all information regardless of language
- Translate field names to English for the structured output
- Preserve original text in 'other_information' when relevant
- Handle currency symbols and number formats appropriate to the language/region
"""

        return f"""
You are an expert multilingual invoice and document parser with exceptional accuracy. Extract ALL structured data from the following text, regardless of the language it's written in.

{language_note}

CRITICAL EXTRACTION REQUIREMENTS:
1. INVOICE IDENTIFICATION:
   - Invoice number, purchase order number, reference numbers
   - Invoice date, due date, issue date (handle various date formats)
   - Document type (invoice, receipt, bill, factura, rechnung, etc.)

2. COMPANY INFORMATION (Extract EVERYTHING):
   - Vendor/Seller: Name, full address, phone, email, tax ID, website
   - Customer/Buyer: Name, full address, phone, email, account number
   - Billing and shipping addresses if different
   - Handle international address formats

3. LINE ITEMS (Extract ALL items with maximum detail):
   - Item name/description (clean and complete, preserve original if non-English)
   - Item category/type if mentioned
   - Quantity (exact numbers, units - handle international number formats)
   - Unit price and total price per item
   - SKU, product codes, part numbers

4. FINANCIAL DETAILS:
   - Subtotal before taxes and fees
   - Tax information: type (VAT, GST, Sales Tax, IVA, MwSt, etc.), rate percentage, amount
   - Discount amounts and descriptions
   - Shipping/delivery charges
   - Additional fees or charges
   - Final total amount
   - Currency (detect from symbols: €, £, ¥, $, etc.)

5. PAYMENT & TERMS:
   - Payment terms (Net 30, Due on receipt, etc.)
   - Payment methods accepted
   - Bank details if provided (IBAN, SWIFT, etc.)
   - Late fees or penalties

6. ADDITIONAL INFORMATION:
   - Notes, comments, special instructions
   - Delivery dates, terms
   - Return policies
   - Warranty information
   - Any handwritten notes or annotations

7. OTHER INFORMATION:
   - Capture ANY other text, numbers, or details that don't fit the above categories
   - Include reference numbers, codes, signatures, stamps
   - Special markings, certifications, compliance info
   - Preserve important non-English text with translations when possible

MULTILINGUAL PROCESSING GUIDELINES:
- Handle OCR errors and formatting issues intelligently across all languages
- Recognize international date formats (DD/MM/YYYY, MM/DD/YYYY, YYYY-MM-DD, etc.)
- Parse numbers with different decimal separators (. vs ,)
- Understand currency symbols and formats from different countries
- Make reasonable assumptions for unclear text in any language
- Ensure all monetary values are positive
- Clean up artifacts while preserving meaning
- If information appears in multiple places, use the most complete version
- For missing standard fields, check if the information exists elsewhere in the document

TEXT TO PARSE:
{text}

Extract according to the InvoiceData schema with maximum accuracy and completeness. Put any information that doesn't fit standard fields into 'other_information'. Handle all languages professionally and accurately.
"""
    


    def translate_to_english(self, text: str, detected_language: str) -> str:
        """Translate non-English text to English using Llama 3.3 70B Versatile."""
        if detected_language == 'en':
            return text  # Already English, no translation needed

        try:
            logger.info(f"Translating {detected_language} text to English using Llama 3.3 70B Versatile")

            translation_prompt = f"""
You are an expert translator specializing in business documents, invoices, and receipts.

TRANSLATION TASK:
- Source Language: {detected_language.upper()}
- Target Language: ENGLISH
- Document Type: Invoice/Receipt/Business Document

TRANSLATION REQUIREMENTS:
1. Translate ALL text accurately while preserving:
   - Numbers, amounts, and quantities exactly as they are
   - Dates in their original format (translate month names if needed)
   - Company names, addresses, and proper nouns
   - Currency symbols and monetary values
   - Invoice numbers, reference numbers, and codes

2. Maintain document structure and formatting
3. Preserve line breaks and spacing
4. Keep technical terms and business terminology accurate
5. Ensure all financial information remains unchanged

ORIGINAL TEXT ({detected_language.upper()}):
{text}

Provide ONLY the English translation, maintaining the exact same structure and format:
"""

            response = self.groq_client.chat.completions.create(
                model="meta-llama/llama-4-maverick-17b-128e-instruct",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert translator for business documents. Translate accurately while preserving all numbers, amounts, dates, and document structure exactly."
                    },
                    {
                        "role": "user",
                        "content": translation_prompt
                    }
                ],
                temperature=0.1,  
                max_tokens=4096
            )

            translated_text = response.choices[0].message.content.strip()
            logger.info(f"Translation completed: {len(text)} -> {len(translated_text)} characters")
            return translated_text

        except Exception as e:
            logger.error(f"Translation failed: {e}")
            logger.info("Proceeding with original text for parsing")
            return text 

    def parse_with_llm(self, text: str, detected_language: Optional[str] = None) -> Optional[InvoiceData]:
        try:

            if not detected_language and self.auto_detect_language:
                detected_language = self.detect_language(text)


            processing_text = text
            processing_language = detected_language or 'en'

            # Only translate if preserve_original_language is False and document is not in English
            if not self.preserve_original_language and detected_language and detected_language != 'en':
                logger.info(f"Non-English document detected ({detected_language}), translating to English...")
                processing_text = self.translate_to_english(text, detected_language)
                processing_language = 'en'
            elif detected_language and detected_language != 'en':
                logger.info(f"Non-English document detected ({detected_language}), preserving original language...")

            prompt = self.create_extraction_prompt(processing_text, processing_language)

            # Update system message based on language
            if processing_language == 'en':
                system_message = "You are an expert invoice parser with exceptional accuracy. Extract structured data accurately from English documents, handling various formats, currencies, and conventions."
            else:
                system_message = f"You are an expert multilingual invoice parser with exceptional accuracy. Extract structured data accurately from documents in {processing_language}, handling various formats, currencies, and conventions. Preserve the original language in the extracted data."

            response = self.client.chat.completions.create(
                model="llama-3.3-70b-versatile",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                response_model=InvoiceData,
                max_retries=3
            )

            return response

        except Exception as e:
            logger.error(f"LLM parsing error: {e}")
            return None
    
    def fallback_regex_extraction(self, text: str) -> InvoiceData:
        logger.info("Using fallback regex extraction")
        
        
        total_patterns = [
            r'total[:\s]*\$?([0-9,]+\.?[0-9]*)',
            r'amount[:\s]*\$?([0-9,]+\.?[0-9]*)',
            r'grand\s+total[:\s]*\$?([0-9,]+\.?[0-9]*)'
        ]
        
        date_patterns = [
            r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            r'(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})',
            r'((?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s+\d{1,2},?\s+\d{4})'
        ]
        
        
        total = 0.0
        for pattern in total_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                total_str = match.group(1).replace(',', '')
                try:
                    total = float(total_str)
                    break
                except ValueError:
                    continue
        
        
        invoice_date = None
        for pattern in date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                invoice_date = match.group(1)
                break
        
        
        items = []
        lines = text.split('\n')
        for line in lines:
            
            price_match = re.search(r'(.+?)\s+\$?([0-9,]+\.?[0-9]*)', line)
            if price_match:
                name = price_match.group(1).strip()
                price_str = price_match.group(2).replace(',', '')
                try:
                    price = float(price_str)
                    if len(name) > 3 and price > 0:
                        items.append(LineItem(name=name, total_price=price))
                except ValueError:
                    continue
        
        return InvoiceData(
            total=total,
            invoice_date=invoice_date,
            items=items
        )
    


    def parse_document(self, file_path: Union[str, Path], language_hint: Optional[str] = None) -> DocumentParsingResult:
        start_time = time.time()
        file_path = Path(file_path)

        if not file_path.exists():
            return DocumentParsingResult(
                success=False,
                error_message=f"File not found: {file_path}"
            )

        if file_path.suffix.lower() not in self.supported_extensions:
            return DocumentParsingResult(
                success=False,
                error_message=f"Unsupported file type: {file_path.suffix}"
            )

        try:
            logger.info(f"Extracting text from {file_path} with multi-language support")
            raw_text = self.extract_text(file_path, language_hint)

            if not raw_text.strip():
                return DocumentParsingResult(
                    success=False,
                    error_message="No text could be extracted from the document",
                    raw_text=raw_text
                )

            
            detected_language = None
            if self.auto_detect_language:
                detected_language = self.detect_language(raw_text)
                logger.info(f"Detected document language: {detected_language}")

            logger.info("Parsing with multilingual LLM")
            invoice_data = self.parse_with_llm(raw_text, detected_language)

            if invoice_data is None:
                logger.warning("LLM parsing failed, using fallback method")
                invoice_data = self.fallback_regex_extraction(raw_text)

            processing_time = time.time() - start_time

            extraction_method = "regex"
            if invoice_data:
                if detected_language and detected_language != 'en':
                    if self.preserve_original_language:
                        extraction_method = f"multilingual_llm_{detected_language}"
                    else:
                        extraction_method = f"translated_llm_{detected_language}_to_en"
                else:
                    extraction_method = "english_llm"

            return DocumentParsingResult(
                success=True,
                invoice_data=invoice_data,
                raw_text=raw_text,
                processing_time=processing_time,
                extraction_method=extraction_method
            )

        except Exception as e:
            logger.error(f"Error parsing document: {e}")
            return DocumentParsingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    


    def batch_parse(self, file_paths: List[Union[str, Path]]) -> List[DocumentParsingResult]:
        results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths, 1):
            logger.info(f"Processing {i}/{total_files}: {file_path}")
            result = self.parse_document(file_path)
            results.append(result)


            if result.success:
                logger.info(f"✓ Successfully processed {file_path}")
            else:
                logger.warning(f"✗ Failed to process {file_path}: {result.error_message}")

        return results

    def export_to_csv(self, results: List[DocumentParsingResult], output_path: Union[str, Path]) -> None:
        output_path = Path(output_path)
        csv_rows = []

        for result in results:
            if result.success and result.invoice_data:
                invoice_rows = result.invoice_data.to_csv_rows()
                csv_rows.extend(invoice_rows)
            else:
                error_row = {
                    'invoice_number': '',
                    'invoice_date': '',
                    'due_date': '',
                    'purchase_order_number': '',
                    'vendor_name': '',
                    'vendor_address': '',
                    'vendor_phone': '',
                    'vendor_email': '',
                    'customer_name': '',
                    'customer_address': '',
                    'customer_phone': '',
                    'customer_email': '',
                    'currency': 'USD',
                    'subtotal': 0,
                    'tax_type': '',
                    'tax_rate': 0,
                    'tax_amount': 0,
                    'discount_amount': 0,
                    'shipping_amount': 0,
                    'total': 0,
                    'payment_terms': '',
                    'payment_method': '',
                    'notes': '',
                    'item_name': f"ERROR: {result.error_message}",
                    'item_category': '',
                    'quantity': 0,
                    'unit_price': 0,
                    'item_total': 0,
                    'other_information': ''
                }
                csv_rows.append(error_row)

        if csv_rows:
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=csv_rows[0].keys())
                writer.writeheader()
                writer.writerows(csv_rows)

            logger.info(f"Enhanced CSV results exported to: {output_path}")
        else:
            logger.warning("No data to export")

    def export_to_json(self, results: List[DocumentParsingResult], output_path: Union[str, Path]) -> None:
        """Export parsing results to JSON format."""
        output_path = Path(output_path)
        json_data = []

        for result in results:
            if result.success and result.invoice_data:
                invoice_dict = result.invoice_data.model_dump()
                result_dict = {
                    'success': True,
                    'file_processed': True,
                    'extraction_method': result.extraction_method,
                    'invoice_data': invoice_dict
                }
            else:
                result_dict = {
                    'success': False,
                    'file_processed': False,
                    'error_message': result.error_message,
                    'extraction_method': result.extraction_method,
                    'invoice_data': None
                }

            json_data.append(result_dict)

        # Write JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, default=str, ensure_ascii=False)

        logger.info(f"JSON results exported to: {output_path}")



