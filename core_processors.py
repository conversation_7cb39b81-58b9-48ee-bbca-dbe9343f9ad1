"""
Core Processing Modules for Agentic Document Processing System

This module contains the essential processing functionality extracted from:
- Standard Mode: PDF and structured document processing
- Handwritten Mode: Handwritten image processing
- RAG Chatbot: Document retrieval and chat functionality

All core functionality is preserved while being integrated into the unified system.
"""

import os
import json
import time
import asyncio
import re
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Union, Tuple
from pathlib import Path
import logging
import tempfile
import base64
from io import BytesIO

# Standard processing imports
import fitz  # PyMuPDF
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
import cv2
import numpy as np
from groq import Groq
import instructor
from pydantic import BaseModel, Field, field_validator, model_validator

# RAG imports
from langchain_groq import ChatGroq
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_chroma import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains import RetrievalQA
from langchain.schema import Document
import pandas as pd

from config import settings

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)

# ================================
# DATA MODELS
# ================================

# Language detection
try:
    from langdetect import detect
    LANGDETECT_AVAILABLE = True
except ImportError:
    LANGDETECT_AVAILABLE = False

# Enhanced data models from standard-mode for better accuracy
class LineItem(BaseModel):
    name: str = Field(..., description="Item name or description", min_length=1)
    quantity: Optional[Union[int, float]] = Field(default=1, description="Quantity of items", ge=0)
    unit_price: Optional[float] = Field(default=None, description="Price per unit", ge=0)
    total_price: float = Field(..., description="Total price for this line item", ge=0)
    category: Optional[str] = Field(default=None, description="Item category")
    description: Optional[str] = Field(default=None, description="Complete detailed description of the item")

    @field_validator('name')
    @classmethod
    def clean_name(cls, v):
        if not v or not v.strip():
            raise ValueError("Item name cannot be empty")

        cleaned = re.sub(r'\s+', ' ', v.strip())
        cleaned = re.sub(r'[^\w\s\-\.\,\(\)\&]', '', cleaned)
        return cleaned.title()

    @field_validator('total_price')
    @classmethod
    def validate_total_price(cls, v):
        if v is None or v < 0:
            raise ValueError("Total price must be non-negative")
        return round(float(v), 2)

class TaxInfo(BaseModel):
    tax_type: Optional[str] = Field(default=None, description="Type of tax (VAT, GST, Sales Tax, etc.)")
    tax_rate: Optional[float] = Field(default=None, description="Tax rate as percentage", ge=0, le=100)
    tax_amount: Optional[float] = Field(default=None, description="Tax amount", ge=0)

class ContactInfo(BaseModel):
    name: Optional[str] = Field(default=None, description="Name of person or company")
    address: Optional[str] = Field(default=None, description="Complete address")
    phone: Optional[str] = Field(default=None, description="Phone number")
    email: Optional[str] = Field(default=None, description="Email address")
    additional_info: Optional[str] = Field(default=None, description="Any additional contact information")

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        if v and '@' not in v:
            return None
        return v

    @field_validator('phone')
    @classmethod
    def clean_phone(cls, v):
        if v:
            cleaned = re.sub(r'[^\d\+\s\-\(\)]', '', v)
            return cleaned.strip() if cleaned else None
        return v

class InvoiceData(BaseModel):
    invoice_number: Optional[str] = Field(default=None, description="Invoice number")
    invoice_date: Optional[Union[datetime, date, str]] = Field(default=None, description="Invoice date")
    due_date: Optional[Union[datetime, date, str]] = Field(default=None, description="Payment due date")
    purchase_order_number: Optional[str] = Field(default=None, description="Purchase order number")

    vendor: Optional[ContactInfo] = Field(default=None, description="Vendor/seller information")
    customer: Optional[ContactInfo] = Field(default=None, description="Customer/buyer information")

    subtotal: Optional[float] = Field(default=None, description="Subtotal before tax", ge=0)
    tax_info: Optional[TaxInfo] = Field(default=None, description="Tax information")
    total: float = Field(..., description="Total amount", ge=0)
    currency: Optional[str] = Field(default="USD", description="Currency code")
    discount_amount: Optional[float] = Field(default=None, description="Discount amount", ge=0)
    shipping_amount: Optional[float] = Field(default=None, description="Shipping/delivery amount", ge=0)
    payment_terms: Optional[str] = Field(default=None, description="Payment terms")
    payment_method: Optional[str] = Field(default=None, description="Payment method")
    notes: Optional[str] = Field(default=None, description="Additional notes")
    other_information: Optional[str] = Field(default=None, description="Other information as a single string. Convert any objects or complex data to string format.")

    items: List[LineItem] = Field(default_factory=list, description="List of line items")

    @field_validator('other_information')
    @classmethod
    def validate_other_information(cls, v):
        """Ensure other_information is always a string"""
        if v is None:
            return None
        if isinstance(v, dict):
            # Convert dict to string representation
            return str(v)
        if isinstance(v, list):
            # Convert list to string representation
            return str(v)
        return str(v)

    def to_csv_rows(self) -> List[dict]:
        """Convert to CSV rows matching sample.csv format - one row per item with complete information"""
        from collections import OrderedDict

        # Collect ALL vendor information
        vendor_name = self.vendor.name if self.vendor else ''
        vendor_address = self.vendor.address if self.vendor else ''
        vendor_phone = self.vendor.phone if self.vendor else ''
        vendor_email = self.vendor.email if self.vendor else ''
        vendor_additional = self.vendor.additional_info if self.vendor and self.vendor.additional_info else ''

        # Collect ALL customer information
        customer_name = self.customer.name if self.customer else ''
        customer_address = self.customer.address if self.customer else ''
        customer_phone = self.customer.phone if self.customer else ''
        customer_email = self.customer.email if self.customer else ''
        customer_additional = self.customer.additional_info if self.customer and self.customer.additional_info else ''

        # Collect ALL other information
        all_other_info = []
        if self.other_information:
            all_other_info.append(self.other_information)
        if vendor_additional:
            all_other_info.append(f"Vendor Additional: {vendor_additional}")
        if customer_additional:
            all_other_info.append(f"Customer Additional: {customer_additional}")

        combined_other_info = '; '.join(all_other_info) if all_other_info else ''

        # Base invoice information (repeated for each item row) - NO GAPS
        base_info = OrderedDict([
            ('invoice_number', self.invoice_number or ''),
            ('invoice_date', str(self.invoice_date) if self.invoice_date else ''),
            ('due_date', str(self.due_date) if self.due_date else ''),
            ('purchase_order_number', self.purchase_order_number or ''),
            ('vendor_name', vendor_name),
            ('vendor_address', vendor_address),
            ('vendor_phone', vendor_phone),
            ('vendor_email', vendor_email),
            ('customer_name', customer_name),
            ('customer_address', customer_address),
            ('customer_phone', customer_phone),
            ('customer_email', customer_email),
            ('currency', self.currency or 'USD'),
            ('subtotal', self.subtotal or 0),
            ('tax_type', self.tax_info.tax_type if self.tax_info else ''),
            ('tax_rate', self.tax_info.tax_rate if self.tax_info else 0),
            ('tax_amount', self.tax_info.tax_amount if self.tax_info else 0),
            ('discount_amount', self.discount_amount or 0),
            ('shipping_amount', self.shipping_amount or 0),
            ('total', self.total or 0),
            ('payment_terms', self.payment_terms or ''),
            ('payment_method', self.payment_method or ''),
            ('notes', self.notes or ''),
            ('other_information', combined_other_info),
            ('item_name', ''),
            ('item_category', ''),
            ('quantity', 0),
            ('unit_price', 0),
            ('item_total', 0)
        ])

        if not self.items:
            return [base_info]

        # Create one row per item (like sample.csv) with complete item information
        rows = []
        for item in self.items:
            row = base_info.copy()

            # Complete item information including description
            item_name = item.name or ''
            if item.description and item.description != item.name:
                item_name = f"{item_name} - {item.description}" if item_name else item.description

            row['item_name'] = item_name
            row['item_category'] = item.category or ''
            row['quantity'] = item.quantity or 1
            row['unit_price'] = item.unit_price or 0
            row['item_total'] = item.total_price or 0
            rows.append(row)

        return rows

class DocumentParsingResult(BaseModel):
    """Result of document parsing operation"""
    success: bool
    invoice_data: Optional[Dict[str, Any]] = None
    raw_text: Optional[str] = None
    processing_time: float = 0.0
    extraction_method: Optional[str] = None
    error_message: Optional[str] = None
    confidence_score: Optional[float] = None

class ProcessingResult(BaseModel):
    """Unified processing result"""
    success: bool
    filename: str
    processing_mode: str
    data: Optional[Dict[str, Any]] = None
    csv_content: Optional[str] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    metadata: Optional[Dict[str, Any]] = None

# ================================
# STANDARD MODE PROCESSOR
# ================================

class StandardModeProcessor:
    """
    Enhanced processor for PDF and structured documents
    Uses the same high-accuracy methods as the root standard-mode implementation
    """

    def __init__(self):
        self.groq_client = Groq(api_key=settings.groq_api_key)
        # Use instructor for structured parsing like the root implementation
        self.client = instructor.from_groq(self.groq_client)
        # Use the better model from root implementation
        self.model = "llama-3.3-70b-versatile"
        self.supported_extensions = {'.pdf', '.txt', '.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.webp', '.gif'}
        self.auto_detect_language = True
        self.preserve_original_language = True

        logger.info("StandardModeProcessor initialized with enhanced accuracy")

    def detect_language(self, text: str) -> Optional[str]:
        """Detect language of the text"""
        if not LANGDETECT_AVAILABLE:
            return None

        try:
            if len(text.strip()) < 10:
                return None

            detected = detect(text)
            logger.info(f"Detected language: {detected}")
            return detected
        except Exception as e:
            logger.warning(f"Language detection failed: {e}")
            return None

    def detect_document_type(self, file_path: Path) -> str:
        """Detect document type based on file extension"""
        extension = file_path.suffix.lower()
        if extension == '.pdf':
            return 'pdf'
        elif extension == '.txt':
            return 'text'
        elif extension in {'.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.webp', '.gif'}:
            return 'image'
        else:
            raise ValueError(f"Unsupported file type: {extension}")
    
    def extract_text_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF using PyMuPDF"""
        try:
            doc = fitz.open(file_path)
            text = ""
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text += page.get_text()
            
            doc.close()
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            # Fallback to OCR
            return self.extract_text_with_ocr(file_path)
    
    def extract_text_with_ocr(self, file_path: Path) -> str:
        """Extract text using OCR (Tesseract)"""
        try:
            image = Image.open(file_path)
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Use Tesseract for OCR
            text = pytesseract.image_to_string(image, config=settings.tesseract_config)
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text with OCR: {e}")
            return ""
    
    def extract_text(self, file_path: Path) -> str:
        """Extract text from document based on type"""
        doc_type = self.detect_document_type(file_path)
        
        if doc_type == 'text':
            return file_path.read_text(encoding='utf-8')
        elif doc_type == 'pdf':
            return self.extract_text_from_pdf(file_path)
        elif doc_type == 'image':
            return self.extract_text_with_ocr(file_path)
        else:
            raise ValueError(f"Unsupported document type: {doc_type}")
    
    def create_extraction_prompt(self, text: str, language: str = 'en') -> str:
        """Create comprehensive extraction prompt for complete information capture"""
        if language == 'en':
            return f"""
            You are an expert document processor. Extract EVERY SINGLE PIECE OF INFORMATION from this document with absolute completeness and accuracy.

            CRITICAL REQUIREMENTS - EXTRACT EVERYTHING:
            1. EVERY number, date, name, address, phone, email, reference number, ID
            2. ALL vendor information - names, addresses, contacts, IDs, registration numbers, tax IDs
            3. ALL customer/buyer information - complete details, account numbers, references
            4. ALL financial data - amounts, taxes, fees, discounts, totals, currencies
            5. ALL line items with complete descriptions, quantities, prices, SKUs, part numbers
            6. ALL additional details - terms, conditions, notes, references, special instructions
            7. ALL other information - anything else present in the document
            8. Do NOT miss any information - be exhaustive and comprehensive

            Text to parse:
            {text}

            Extract and structure ALL information with maximum precision. Capture EVERYTHING - no detail should be left out.
            If any information doesn't fit standard fields, include it in other_information field as a single string.
            Convert any complex data to string format. Be absolutely thorough and comprehensive.
            """
        else:
            return f"""
            You are an expert multilingual document processor. Extract EVERY SINGLE PIECE OF INFORMATION from this {language} document with absolute completeness and accuracy.

            CRITICAL REQUIREMENTS - EXTRACT EVERYTHING:
            1. EVERY number, date, name, address, phone, email, reference number, ID
            2. ALL vendor information - names, addresses, contacts, IDs, registration numbers
            3. ALL customer/buyer information - complete details, account numbers, references
            4. ALL financial data - amounts, taxes, fees, discounts, totals, currencies
            5. ALL line items with complete descriptions, quantities, prices, SKUs, part numbers
            6. ALL additional details - terms, conditions, notes, references, special instructions
            7. ALL other information - anything else present in the document
            8. Preserve original language in extracted data

            Text to parse:
            {text}

            Extract and structure ALL information with maximum precision, maintaining the original {language} language.
            Capture EVERYTHING - no detail should be left out. Be absolutely thorough and comprehensive.
            If any information doesn't fit standard fields, include it in other_information field as a single string.
            Convert any complex data to string format.
            """

    def parse_with_llm(self, text: str, detected_language: Optional[str] = None) -> Optional[InvoiceData]:
        """Enhanced parsing using instructor and structured models like root implementation"""
        try:
            # Detect language if not provided
            if not detected_language and self.auto_detect_language:
                detected_language = self.detect_language(text)

            processing_text = text
            processing_language = detected_language or 'en'

            # Create appropriate prompt
            prompt = self.create_extraction_prompt(processing_text, processing_language)

            # Update system message based on language
            if processing_language == 'en':
                system_message = "You are an expert document processor with exceptional accuracy. Your task is to extract EVERY SINGLE PIECE OF INFORMATION from documents with absolute completeness. Extract ALL data - vendor details, customer details, financial information, line items, dates, numbers, addresses, contacts, references, terms, conditions, notes, and ANY other information present. Be exhaustive and comprehensive - miss nothing. Handle various formats, currencies, and conventions. If information doesn't fit standard fields, include it in other_information as a single string. Always return strings for text fields, never objects."
            else:
                system_message = f"You are an expert multilingual document processor with exceptional accuracy. Your task is to extract EVERY SINGLE PIECE OF INFORMATION from {processing_language} documents with absolute completeness. Extract ALL data - vendor details, customer details, financial information, line items, dates, numbers, addresses, contacts, references, terms, conditions, notes, and ANY other information present. Be exhaustive and comprehensive - miss nothing. Preserve the original {processing_language} language in extracted data. Handle various formats, currencies, and conventions. If information doesn't fit standard fields, include it in other_information as a single string. Always return strings for text fields, never objects."

            # Use instructor for structured parsing (like root implementation)
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                response_model=InvoiceData,
                max_retries=3
            )

            return response

        except Exception as e:
            logger.error(f"Enhanced LLM parsing error: {e}")
            return None

    def fallback_regex_extraction(self, text: str) -> InvoiceData:
        """Fallback regex extraction like root implementation"""
        logger.info("Using fallback regex extraction")

        # Total patterns
        total_patterns = [
            r'total[:\s]*\$?([0-9,]+\.?[0-9]*)',
            r'amount[:\s]*\$?([0-9,]+\.?[0-9]*)',
            r'grand\s+total[:\s]*\$?([0-9,]+\.?[0-9]*)'
        ]

        date_patterns = [
            r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            r'(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})',
            r'((?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s+\d{1,2},?\s+\d{4})'
        ]

        # Extract total
        total = 0.0
        for pattern in total_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                total_str = match.group(1).replace(',', '')
                try:
                    total = float(total_str)
                    break
                except ValueError:
                    continue

        # Extract date
        invoice_date = None
        for pattern in date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                invoice_date = match.group(1)
                break

        # Create basic line items
        items = []
        lines = text.split('\n')
        for line in lines:
            # Simple line item detection
            if re.search(r'\$\d+', line) and len(line.strip()) > 5:
                try:
                    price_match = re.search(r'\$?([0-9,]+\.?[0-9]*)', line)
                    if price_match:
                        price = float(price_match.group(1).replace(',', ''))
                        description = re.sub(r'\$[0-9,]+\.?[0-9]*', '', line).strip()
                        if description:
                            items.append(LineItem(
                                name=description[:50],  # Limit length
                                total_price=price
                            ))
                except:
                    continue

        return InvoiceData(
            invoice_date=invoice_date,
            total=total,
            items=items
        )
    
    async def process_document(self, filename: str, file_content: bytes) -> ProcessingResult:
        """Main processing function for standard mode"""
        start_time = time.time()
        
        try:
            # Save file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                temp_file.write(file_content)
                temp_path = Path(temp_file.name)
            
            try:
                # Extract text with enhanced methods
                logger.info(f"Extracting text from {filename} with multi-language support")
                raw_text = self.extract_text(temp_path)

                if not raw_text.strip():
                    return ProcessingResult(
                        success=False,
                        filename=filename,
                        processing_mode="STANDARD_MODE",
                        error_message="No text could be extracted from the document",
                        processing_time=time.time() - start_time
                    )

                # Detect language for better processing
                detected_language = None
                if self.auto_detect_language:
                    detected_language = self.detect_language(raw_text)
                    logger.info(f"Detected document language: {detected_language}")

                # Parse with enhanced LLM using instructor
                logger.info("Parsing with enhanced multilingual LLM")
                invoice_data = self.parse_with_llm(raw_text, detected_language)

                if invoice_data is None:
                    logger.warning("Enhanced LLM parsing failed, using fallback method")
                    invoice_data = self.fallback_regex_extraction(raw_text)

                # Convert InvoiceData to CSV format
                if isinstance(invoice_data, InvoiceData):
                    # Use the to_csv_rows method for proper formatting
                    csv_content = self._generate_csv_from_invoice_data(invoice_data)
                    extraction_method = "enhanced_llm"
                    if detected_language and detected_language != 'en':
                        if self.preserve_original_language:
                            extraction_method = f"multilingual_llm_{detected_language}"
                        else:
                            extraction_method = f"translated_llm_{detected_language}_to_en"
                    invoice_dict = invoice_data.model_dump()
                else:
                    invoice_dict = invoice_data or {"raw_text": raw_text, "extraction_method": "fallback"}
                    extraction_method = "fallback"
                    # Generate CSV content using fallback method
                    csv_content = self._generate_csv_content(invoice_dict, filename)

                processing_time = time.time() - start_time

                return ProcessingResult(
                    success=True,
                    filename=filename,
                    processing_mode="STANDARD_MODE",
                    data=invoice_dict,
                    csv_content=csv_content,
                    processing_time=processing_time,
                    metadata={
                        "extraction_method": extraction_method,
                        "text_length": len(raw_text),
                        "has_structured_data": bool(invoice_dict),
                        "detected_language": detected_language,
                        "processing_accuracy": "enhanced"
                    }
                )
                
            finally:
                # Cleanup temp file
                if temp_path.exists():
                    temp_path.unlink()
                    
        except Exception as e:
            logger.error(f"Error processing document in standard mode: {e}")
            return ProcessingResult(
                success=False,
                filename=filename,
                processing_mode="STANDARD_MODE",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def _generate_csv_from_invoice_data(self, invoice_data: InvoiceData) -> str:
        """Generate CSV content from InvoiceData matching sample.csv format with NO gaps"""
        try:
            import io
            import csv

            output = io.StringIO()
            # Use comma delimiter, strict formatting to prevent gaps
            writer = csv.writer(output, delimiter=',', quoting=csv.QUOTE_MINIMAL, lineterminator='\n')

            # Fixed headers matching sample.csv exactly
            headers = [
                'invoice_number', 'invoice_date', 'due_date', 'purchase_order_number',
                'vendor_name', 'vendor_address', 'vendor_phone', 'vendor_email',
                'customer_name', 'customer_address', 'customer_phone', 'customer_email',
                'currency', 'subtotal', 'tax_type', 'tax_rate', 'tax_amount', 'discount_amount', 'shipping_amount',
                'total', 'payment_terms', 'payment_method', 'notes', 'other_information',
                'item_name', 'item_category', 'quantity', 'unit_price', 'item_total'
            ]
            writer.writerow(headers)

            # Get CSV rows from InvoiceData (one row per item) - NO empty rows
            rows = invoice_data.to_csv_rows()
            for row in rows:
                # Ensure no None values that could create gaps
                clean_values = []
                for value in row.values():
                    if value is None:
                        clean_values.append('')
                    else:
                        clean_values.append(str(value))
                writer.writerow(clean_values)

            # Get the content and remove any extra newlines
            content = output.getvalue()
            # Remove any double newlines that might create gaps
            content = content.replace('\n\n', '\n')
            # Remove trailing newline to prevent empty line at end
            content = content.rstrip('\n')
            return content

        except Exception as e:
            logger.error(f"Error generating CSV from InvoiceData: {e}")
            return f"invoice_number,error\n,Error generating CSV: {str(e)}\n"

    def _generate_csv_content(self, data: Dict[str, Any], filename: str) -> str:
        """Generate CSV content matching sample.csv format for fallback cases with NO gaps"""
        try:
            import io
            import csv
            from collections import OrderedDict

            output = io.StringIO()
            # Use comma delimiter, strict formatting to prevent gaps
            writer = csv.writer(output, delimiter=',', quoting=csv.QUOTE_MINIMAL, lineterminator='\n')

            # Fixed headers matching sample.csv exactly
            headers = [
                'invoice_number', 'invoice_date', 'due_date', 'purchase_order_number',
                'vendor_name', 'vendor_address', 'vendor_phone', 'vendor_email',
                'customer_name', 'customer_address', 'customer_phone', 'customer_email',
                'currency', 'subtotal', 'tax_type', 'tax_rate', 'tax_amount', 'discount_amount', 'shipping_amount',
                'total', 'payment_terms', 'payment_method', 'notes', 'other_information',
                'item_name', 'item_category', 'quantity', 'unit_price', 'item_total'
            ]
            writer.writerow(headers)

            # Create base row structure matching sample.csv format
            base_info = OrderedDict([
                ('invoice_number', data.get('invoice_number', '')),
                ('invoice_date', str(data.get('invoice_date', '')) if data.get('invoice_date') else ''),
                ('due_date', str(data.get('due_date', '')) if data.get('due_date') else ''),
                ('purchase_order_number', data.get('purchase_order_number', '')),
                ('vendor_name', ''),
                ('vendor_address', ''),
                ('vendor_phone', ''),
                ('vendor_email', ''),
                ('customer_name', ''),
                ('customer_address', ''),
                ('customer_phone', ''),
                ('customer_email', ''),
                ('currency', data.get('currency', 'USD')),
                ('subtotal', data.get('subtotal', 0)),
                ('tax_type', ''),
                ('tax_rate', 0),
                ('tax_amount', 0),
                ('discount_amount', data.get('discount_amount', 0)),
                ('shipping_amount', data.get('shipping_amount', 0)),
                ('total', data.get('total', 0) or data.get('total_amount', 0)),
                ('payment_terms', data.get('payment_terms', '')),
                ('payment_method', data.get('payment_method', '')),
                ('notes', data.get('notes', '')),
                ('other_information', data.get('other_information', '')),
                ('item_name', ''),
                ('item_category', ''),
                ('quantity', 0),
                ('unit_price', 0),
                ('item_total', 0)
            ])

            # Extract vendor info
            vendor = data.get('vendor', {}) or {}
            if isinstance(vendor, dict):
                base_info['vendor_name'] = vendor.get('name', '')
                base_info['vendor_address'] = vendor.get('address', '')
                base_info['vendor_phone'] = vendor.get('phone', '')
                base_info['vendor_email'] = vendor.get('email', '')

            # Extract customer info
            customer = data.get('customer', {}) or {}
            if isinstance(customer, dict):
                base_info['customer_name'] = customer.get('name', '')
                base_info['customer_address'] = customer.get('address', '')
                base_info['customer_phone'] = customer.get('phone', '')
                base_info['customer_email'] = customer.get('email', '')

            # Extract tax info
            tax_info = data.get('tax_info', {}) or {}
            if isinstance(tax_info, dict):
                base_info['tax_type'] = tax_info.get('tax_type', '')
                base_info['tax_rate'] = tax_info.get('tax_rate', 0)
                base_info['tax_amount'] = tax_info.get('tax_amount', 0)
            else:
                base_info['tax_amount'] = data.get('tax_amount', 0)

            # Handle line items - create one row per item like sample.csv with no gaps
            items = data.get('items', []) or []
            if not items or not isinstance(items, list):
                # No items, write single row with base info - ensure no None values
                clean_values = []
                for value in base_info.values():
                    if value is None:
                        clean_values.append('')
                    else:
                        clean_values.append(str(value))
                writer.writerow(clean_values)
            else:
                # Write one row per item (like sample.csv) with complete information
                for item in items:
                    row = base_info.copy()
                    if isinstance(item, dict):
                        # Complete item information
                        item_name = item.get('name', '')
                        item_desc = item.get('description', '')
                        if item_desc and item_desc != item_name:
                            item_name = f"{item_name} - {item_desc}" if item_name else item_desc

                        row['item_name'] = item_name
                        row['item_category'] = item.get('category', '')
                        row['quantity'] = item.get('quantity', 1)
                        row['unit_price'] = item.get('unit_price', 0)
                        row['item_total'] = item.get('total_price', 0)

                    # Ensure no None values that could create gaps
                    clean_values = []
                    for value in row.values():
                        if value is None:
                            clean_values.append('')
                        else:
                            clean_values.append(str(value))
                    writer.writerow(clean_values)

            # Get the content and remove any extra newlines
            content = output.getvalue()
            # Remove any double newlines that might create gaps
            content = content.replace('\n\n', '\n')
            # Remove trailing newline to prevent empty line at end
            content = content.rstrip('\n')
            return content

        except Exception as e:
            logger.error(f"Error generating enhanced CSV content: {e}")
            return f"filename,error\n{filename},Error generating CSV: {str(e)}\n"

# ================================
# RAG CHATBOT PROCESSOR
# ================================

class RAGChatbotProcessor:
    """
    Core processor for RAG-based document chatbot
    Extracted from rag-chatbot functionality
    """

    def __init__(self):
        self.groq_client = Groq(api_key=settings.groq_api_key)
        self.llm = None
        self.embeddings = None
        self.vector_store = None
        self.qa_chain = None
        self.documents_loaded = 0

        # Initialize components (will be done lazily when needed)
        self._initialized = False

        logger.info("RAGChatbotProcessor initialized")

    async def _initialize_components(self):
        """Initialize LLM, embeddings, and vector store"""
        try:
            # Initialize Groq LLM
            self.llm = ChatGroq(
                groq_api_key=settings.groq_api_key,
                model_name=settings.rag_llm_model,
                temperature=settings.rag_temperature,
                max_tokens=settings.rag_max_tokens
            )

            # Initialize HuggingFace embeddings
            self.embeddings = HuggingFaceEmbeddings(
                model_name=settings.embedding_model,
                model_kwargs={'device': settings.embedding_device},
                encode_kwargs={'normalize_embeddings': settings.normalize_embeddings}
            )

            # Initialize vector store
            await self._initialize_vector_store()

            # Load existing documents
            await self._load_documents()

            logger.info("RAG components initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing RAG components: {e}")

    async def _initialize_vector_store(self):
        """Initialize ChromaDB vector store"""
        try:
            self.vector_store = Chroma(
                persist_directory=settings.chroma_persist_directory,
                embedding_function=self.embeddings,
                collection_name=settings.chroma_collection_name
            )
            logger.info("Vector store initialized")
        except Exception as e:
            logger.error(f"Error initializing vector store: {e}")

    async def _clear_vector_database(self) -> int:
        """
        Completely clear the vector database by deleting the collection and recreating it.
        Returns the number of chunks that were cleared.
        """
        chunks_cleared = 0

        try:
            if self.vector_store:
                # Get current document count before clearing
                try:
                    # Try to get collection info to count documents
                    collection = self.vector_store._collection
                    if collection:
                        chunks_cleared = collection.count()
                        logger.info(f"Found {chunks_cleared} chunks to clear")
                except Exception as e:
                    logger.warning(f"Could not count existing chunks: {e}")
                    chunks_cleared = 0

                # Delete the entire collection (this removes all chunks and embeddings)
                try:
                    self.vector_store.delete_collection()
                    logger.info("Vector database collection deleted successfully - all chunks and embeddings removed")
                except Exception as e:
                    logger.warning(f"Error deleting vector collection: {e}")

                # Recreate the vector store (completely empty)
                await self._initialize_vector_store()
                logger.info("Vector database reinitialized (completely empty)")

                # Verify it's empty
                try:
                    new_count = self.vector_store._collection.count() if self.vector_store._collection else 0
                    logger.info(f"Vector database now contains {new_count} chunks (should be 0)")
                except Exception as e:
                    logger.warning(f"Could not verify empty database: {e}")

            return chunks_cleared

        except Exception as e:
            logger.error(f"Error clearing vector database: {e}")
            return chunks_cleared

    async def _load_documents(self):
        """Load documents from CSV files"""
        try:
            documents = []
            csv_dirs = [Path(settings.csv_results_dir)] + [Path(d) for d in settings.get_additional_search_dirs_list()]

            for csv_dir in csv_dirs:
                if csv_dir.exists():
                    csv_files = list(csv_dir.glob("*.csv"))
                    for csv_file in csv_files:
                        try:
                            df = pd.read_csv(csv_file)
                            for _, row in df.iterrows():
                                # Create document content from CSV row
                                content = self._create_document_content(row)
                                doc = Document(
                                    page_content=content,
                                    metadata={
                                        "source": str(csv_file),
                                        "filename": row.get('filename', ''),
                                        "invoice_number": row.get('invoice_number', ''),
                                        "vendor_name": row.get('vendor_name', ''),
                                        "total_amount": row.get('total_amount', '')
                                    }
                                )
                                documents.append(doc)
                        except Exception as e:
                            logger.error(f"Error loading CSV file {csv_file}: {e}")

            if documents:
                # Split documents into chunks
                text_splitter = RecursiveCharacterTextSplitter(
                    chunk_size=settings.chunk_size,
                    chunk_overlap=settings.chunk_overlap
                )
                split_docs = text_splitter.split_documents(documents)

                # Add to vector store
                if self.vector_store:
                    self.vector_store.add_documents(split_docs)
                    self.documents_loaded = len(split_docs)

                    # Create QA chain
                    self.qa_chain = RetrievalQA.from_chain_type(
                        llm=self.llm,
                        chain_type="stuff",
                        retriever=self.vector_store.as_retriever(search_kwargs={"k": settings.max_retrieval_docs}),
                        return_source_documents=True
                    )

                    logger.info(f"Loaded {self.documents_loaded} document chunks into vector store")

        except Exception as e:
            logger.error(f"Error loading documents: {e}")

    def _create_document_content(self, row: pd.Series) -> str:
        """Create searchable content from CSV row"""
        content_parts = []

        # Add all non-empty fields
        for field, value in row.items():
            if pd.notna(value) and str(value).strip():
                content_parts.append(f"{field}: {value}")

        return "\n".join(content_parts)

    async def chat(self, message: str, session_id: Optional[str] = None) -> Dict[str, Any]:
        """Process chat message and return response"""
        try:
            # Initialize if not already done
            if not self._initialized:
                await self._initialize_components()
                self._initialized = True

            if not self.qa_chain:
                return {
                    "success": False,
                    "response": "PLEASE UPLOAD DOCUMENTS FIRST.",
                    "session_id": session_id or "unknown",
                    "sources": [],
                    "error": "RAG system not initialized"
                }

            # Query the RAG system
            result = self.qa_chain({"query": message})

            # Extract sources
            sources = []
            if "source_documents" in result:
                for doc in result["source_documents"]:
                    source_info = doc.metadata.get("source", "Unknown")
                    if source_info not in sources:
                        sources.append(source_info)

            return {
                "success": True,
                "response": result["result"],
                "session_id": session_id or "unknown",
                "sources": sources,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            logger.error(f"Error processing chat message: {e}")
            return {
                "success": False,
                "response": f"NO DOCUMENTS TO CHAT WITH. Please upload documents first.",
                "session_id": session_id or "unknown",
                "sources": [],
                "error": str(e)
            }

    async def update_rag_database(self) -> Dict[str, Any]:
        """
        Update the RAG database with latest documents WITHOUT deleting CSV files.
        This is called after each document is processed to make it available for chat.
        """
        try:
            # Initialize if not already done
            if not self._initialized:
                await self._initialize_components()
                self._initialized = True

            # Just reload documents into vector store (don't delete anything)
            await self._load_documents()

            return {
                "success": True,
                "message": f"RAG database updated successfully. Loaded {self.documents_loaded} document chunks.",
                "documents_loaded": self.documents_loaded
            }

        except Exception as e:
            logger.error(f"Error updating RAG database: {e}")
            return {
                "success": False,
                "message": f"Error updating RAG database: {str(e)}",
                "error": str(e),
                "documents_loaded": 0
            }

    async def refresh_database(self) -> Dict[str, Any]:
        """
        Completely refresh the database by:
        1. Deleting all CSV files from results directory
        2. Clearing the vector database completely (including all chunks)
        3. Reloading any remaining documents (if any)

        WARNING: This permanently deletes all processed document data!
        """
        try:
            # Initialize if not already done
            if not self._initialized:
                await self._initialize_components()
                self._initialized = True

            # Step 1: Delete all CSV files from results directory
            deleted_files = await self._delete_all_csv_files()

            # Step 2: Clear existing vector store completely (including all chunks)
            chunks_cleared = await self._clear_vector_database()
            logger.info(f"Vector database cleared - {chunks_cleared} chunks removed")

            # Step 3: Verify no documents remain (should be empty since all CSV files were deleted)
            await self._load_documents()

            # If documents were still loaded after refresh, it means there are CSV files in other directories
            if self.documents_loaded > 0:
                logger.warning(f"Warning: {self.documents_loaded} documents still loaded after refresh. This may indicate CSV files in additional directories that weren't cleaned.")
                logger.warning("Consider checking additional search directories for remaining CSV files.")

            return {
                "success": True,
                "message": f"Database completely refreshed. Deleted {deleted_files} CSV files and cleared vector database (all chunks removed). Loaded {self.documents_loaded} document chunks.",
                "csv_files_deleted": deleted_files,
                "documents_loaded": self.documents_loaded,
                "vector_db_cleared": True
            }

        except Exception as e:
            logger.error(f"Error refreshing database: {e}")
            return {
                "success": False,
                "message": f"Error refreshing database: {str(e)}",
                "error": str(e),
                "csv_files_deleted": 0,
                "documents_loaded": 0,
                "vector_db_cleared": False
            }

    async def _delete_all_csv_files(self) -> int:
        """Delete all CSV files from the results directory and additional search directories"""
        deleted_count = 0

        try:
            # Get all directories to clean
            directories_to_clean = [Path(settings.csv_results_dir)]

            # Add additional search directories
            additional_dirs = settings.get_additional_search_dirs_list()
            logger.info(f"Additional search directories configured: {additional_dirs}")

            for dir_path_str in additional_dirs:
                dir_path = Path(dir_path_str)
                if dir_path.exists() and dir_path.is_dir():
                    directories_to_clean.append(dir_path)
                    logger.info(f"Will clean CSV files from: {dir_path}")
                else:
                    logger.info(f"Directory does not exist or is not a directory: {dir_path}")

            logger.info(f"Total directories to clean: {len(directories_to_clean)}")

            # Delete CSV files from each directory
            for directory in directories_to_clean:
                if directory.exists() and directory.is_dir():
                    csv_files = list(directory.glob("*.csv"))

                    if csv_files:
                        logger.info(f"Found {len(csv_files)} CSV files in {directory}")
                        for csv_file in csv_files:
                            try:
                                csv_file.unlink()  # Delete the file
                                deleted_count += 1
                                logger.info(f"Deleted CSV file: {csv_file}")
                            except Exception as e:
                                logger.error(f"Error deleting CSV file {csv_file}: {e}")
                    else:
                        logger.info(f"No CSV files found in {directory}")

                    logger.info(f"Cleaned {len(csv_files)} CSV files from {directory}")
                else:
                    logger.warning(f"Directory does not exist: {directory}")

            logger.info(f"Total CSV files deleted across all directories: {deleted_count}")
            return deleted_count

        except Exception as e:
            logger.error(f"Error deleting CSV files: {e}")
            return deleted_count

    def get_database_status(self) -> Dict[str, Any]:
        """Get current database status"""
        return {
            "status": "healthy" if self.qa_chain else "not_initialized",
            "document_count": self.documents_loaded,
            "last_updated": time.strftime("%Y-%m-%d %H:%M:%S"),
            "vector_store_configured": bool(self.vector_store),
            "llm_configured": bool(self.llm),
            "embeddings_configured": bool(self.embeddings)
        }

# ================================
# HANDWRITTEN MODE PROCESSOR
# ================================

class HandwrittenModeProcessor:
    """
    Enhanced processor for handwritten documents and images
    Uses the same high-accuracy vision models as the root handwritten-mode implementation
    """

    def __init__(self):
        self.groq_client = Groq(api_key=settings.groq_api_key)
        # Use the same models as root implementation for maximum accuracy
        self.vision_model = "meta-llama/llama-4-maverick-17b-128e-instruct"  # Vision model for image analysis
        self.text_model = "llama-3.3-70b-versatile"  # Text model for structured data extraction
        self.supported_extensions = {'.png', '.jpg', '.jpeg', '.tiff', '.bmp'}

        logger.info("HandwrittenModeProcessor initialized with enhanced vision models")
    
    def preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image for better OCR accuracy"""
        try:
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.2)
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            # Apply slight noise reduction
            image = image.filter(ImageFilter.MedianFilter(size=3))
            
            return image
            
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            return image
    
    def split_image_into_sections(self, image: Image.Image, sections: int = 3, overlap: float = 0.2) -> List[Image.Image]:
        """Split image into overlapping sections for better processing"""
        try:
            width, height = image.size
            section_height = height // sections
            overlap_pixels = int(section_height * overlap)
            
            image_sections = []
            
            for i in range(sections):
                start_y = max(0, i * section_height - overlap_pixels)
                end_y = min(height, (i + 1) * section_height + overlap_pixels)
                
                section = image.crop((0, start_y, width, end_y))
                image_sections.append(section)
            
            return image_sections
            
        except Exception as e:
            logger.error(f"Error splitting image: {e}")
            return [image]
    
    def extract_handwritten_text(self, image: Image.Image) -> str:
        """Extract text from handwritten image using Groq vision model"""
        try:
            # Convert image to base64
            buffer = BytesIO()
            image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            # Use enhanced Groq vision model for maximum accuracy
            response = self.groq_client.chat.completions.create(
                model=self.vision_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Extract all text from this handwritten document. Be very careful to capture every word, number, and detail accurately. Preserve the original structure and formatting as much as possible."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                temperature=0.1,
                max_tokens=1500
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error extracting handwritten text: {e}")
            return ""
    
    def extract_invoice_data_with_llm(self, text: str) -> Optional[InvoiceData]:
        """Enhanced structured data extraction from handwritten text using instructor"""
        try:
            # Use instructor for structured parsing like the enhanced standard mode
            client = instructor.from_groq(self.groq_client)

            prompt = f"""
            Extract structured invoice/document data from this handwritten text with high accuracy.
            Pay special attention to handwritten numbers, dates, and amounts which may be unclear.

            Handwritten text:
            {text}

            Extract all available information with maximum precision.
            """

            response = client.chat.completions.create(
                model=self.text_model,  # Use enhanced text model
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at reading handwritten documents and extracting structured data with exceptional accuracy. Handle unclear handwriting by making reasonable interpretations based on context."
                    },
                    {"role": "user", "content": prompt}
                ],
                response_model=InvoiceData,
                max_retries=3,
                temperature=0.1
            )

            return response

        except Exception as e:
            logger.error(f"Enhanced handwritten LLM extraction error: {e}")
            # Fallback to basic extraction
            try:
                basic_response = self.groq_client.chat.completions.create(
                    model=self.text_model,
                    messages=[
                        {
                            "role": "system",
                            "content": "Extract key information from handwritten text. Focus on total amount, dates, and vendor/customer names."
                        },
                        {"role": "user", "content": f"Extract data from: {text}"}
                    ],
                    temperature=0.1,
                    max_tokens=1000
                )

                # Create basic InvoiceData from response
                content = basic_response.choices[0].message.content

                # Extract total using regex
                total_match = re.search(r'total[:\s]*\$?([0-9,]+\.?[0-9]*)', content, re.IGNORECASE)
                total = float(total_match.group(1).replace(',', '')) if total_match else 0.0

                return InvoiceData(total=total, items=[])

            except Exception as fallback_error:
                logger.error(f"Fallback extraction also failed: {fallback_error}")
                return None
    
    async def process_document(self, filename: str, file_content: bytes) -> ProcessingResult:
        """Main processing function for handwritten mode"""
        start_time = time.time()
        
        try:
            # Load image from bytes
            image = Image.open(BytesIO(file_content))
            
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            # Split into sections for better processing
            sections = self.split_image_into_sections(processed_image, 
                                                    sections=settings.default_sections, 
                                                    overlap=settings.section_overlap)
            
            # Extract text from each section
            text_sections = []
            for section in sections:
                section_text = self.extract_handwritten_text(section)
                if section_text.strip():
                    text_sections.append(section_text)
            
            # Combine all text
            combined_text = "\n\n".join(text_sections)
            
            if not combined_text.strip():
                return ProcessingResult(
                    success=False,
                    filename=filename,
                    processing_mode="HANDWRITTEN_MODE",
                    error_message="No text could be extracted from the handwritten document",
                    processing_time=time.time() - start_time
                )
            
            # Extract structured data with enhanced methods
            logger.info(f"Extracting structured data from handwritten text ({len(combined_text)} chars)")
            invoice_data = self.extract_invoice_data_with_llm(combined_text)

            if invoice_data is None:
                logger.warning("Enhanced handwritten extraction failed, using fallback")
                # Create basic fallback data
                invoice_dict = {
                    "raw_text": combined_text,
                    "extraction_method": "handwritten_fallback",
                    "total": 0.0
                }
                extraction_method = "handwritten_fallback"
                # Generate CSV content using fallback method
                csv_content = self._generate_csv_content(invoice_dict, filename)
            else:
                # Use the to_csv_rows method for proper formatting
                csv_content = self._generate_csv_from_invoice_data(invoice_data)
                extraction_method = "enhanced_handwritten_vision"
                invoice_dict = invoice_data.model_dump()

            processing_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                filename=filename,
                processing_mode="HANDWRITTEN_MODE",
                data=invoice_dict,
                csv_content=csv_content,
                processing_time=processing_time,
                metadata={
                    "extraction_method": extraction_method,
                    "sections_processed": len(sections),
                    "text_length": len(combined_text),
                    "image_size": list(image.size),
                    "processing_accuracy": "enhanced"
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing handwritten document: {e}")
            return ProcessingResult(
                success=False,
                filename=filename,
                processing_mode="HANDWRITTEN_MODE",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def _generate_csv_from_invoice_data(self, invoice_data: InvoiceData) -> str:
        """Generate CSV content from InvoiceData matching sample.csv format with NO gaps"""
        try:
            import io
            import csv

            output = io.StringIO()
            # Use comma delimiter, strict formatting to prevent gaps
            writer = csv.writer(output, delimiter=',', quoting=csv.QUOTE_MINIMAL, lineterminator='\n')

            # Fixed headers matching sample.csv exactly
            headers = [
                'invoice_number', 'invoice_date', 'due_date', 'purchase_order_number',
                'vendor_name', 'vendor_address', 'vendor_phone', 'vendor_email',
                'customer_name', 'customer_address', 'customer_phone', 'customer_email',
                'currency', 'subtotal', 'tax_type', 'tax_rate', 'tax_amount', 'discount_amount', 'shipping_amount',
                'total', 'payment_terms', 'payment_method', 'notes', 'other_information',
                'item_name', 'item_category', 'quantity', 'unit_price', 'item_total'
            ]
            writer.writerow(headers)

            # Get CSV rows from InvoiceData (one row per item) - NO empty rows
            rows = invoice_data.to_csv_rows()
            for row in rows:
                # Ensure no None values that could create gaps
                clean_values = []
                for value in row.values():
                    if value is None:
                        clean_values.append('')
                    else:
                        clean_values.append(str(value))
                writer.writerow(clean_values)

            # Get the content and remove any extra newlines
            content = output.getvalue()
            # Remove any double newlines that might create gaps
            content = content.replace('\n\n', '\n')
            # Remove trailing newline to prevent empty line at end
            content = content.rstrip('\n')
            return content

        except Exception as e:
            logger.error(f"Error generating CSV from InvoiceData: {e}")
            return f"invoice_number,error\n,Error generating CSV: {str(e)}\n"

    def _generate_csv_content(self, data: Dict[str, Any], filename: str) -> str:
        """Generate CSV content matching sample.csv format for handwritten mode fallback with NO gaps"""
        try:
            import io
            import csv
            from collections import OrderedDict

            output = io.StringIO()
            # Use comma delimiter, strict formatting to prevent gaps
            writer = csv.writer(output, delimiter=',', quoting=csv.QUOTE_MINIMAL, lineterminator='\n')

            # Fixed headers matching sample.csv exactly
            headers = [
                'invoice_number', 'invoice_date', 'due_date', 'purchase_order_number',
                'vendor_name', 'vendor_address', 'vendor_phone', 'vendor_email',
                'customer_name', 'customer_address', 'customer_phone', 'customer_email',
                'currency', 'subtotal', 'tax_type', 'tax_rate', 'tax_amount', 'discount_amount', 'shipping_amount',
                'total', 'payment_terms', 'payment_method', 'notes', 'other_information',
                'item_name', 'item_category', 'quantity', 'unit_price', 'item_total'
            ]
            writer.writerow(headers)

            # Create base row structure matching sample.csv format
            base_info = OrderedDict([
                ('invoice_number', data.get('invoice_number', '')),
                ('invoice_date', str(data.get('invoice_date', '')) if data.get('invoice_date') else ''),
                ('due_date', str(data.get('due_date', '')) if data.get('due_date') else ''),
                ('purchase_order_number', data.get('purchase_order_number', '')),
                ('vendor_name', ''),
                ('vendor_address', ''),
                ('vendor_phone', ''),
                ('vendor_email', ''),
                ('customer_name', ''),
                ('customer_address', ''),
                ('customer_phone', ''),
                ('customer_email', ''),
                ('currency', data.get('currency', 'USD')),
                ('subtotal', data.get('subtotal', 0)),
                ('tax_type', ''),
                ('tax_rate', 0),
                ('tax_amount', 0),
                ('discount_amount', data.get('discount_amount', 0)),
                ('shipping_amount', data.get('shipping_amount', 0)),
                ('total', data.get('total', 0) or data.get('total_amount', 0)),
                ('payment_terms', data.get('payment_terms', '')),
                ('payment_method', data.get('payment_method', '')),
                ('notes', data.get('notes', '')),
                ('other_information', data.get('other_information', '')),
                ('item_name', ''),
                ('item_category', ''),
                ('quantity', 0),
                ('unit_price', 0),
                ('item_total', 0)
            ])

            # Extract vendor info
            vendor = data.get('vendor', {}) or {}
            if isinstance(vendor, dict):
                base_info['vendor_name'] = vendor.get('name', '')
                base_info['vendor_address'] = vendor.get('address', '')
                base_info['vendor_phone'] = vendor.get('phone', '')
                base_info['vendor_email'] = vendor.get('email', '')

            # Extract customer info
            customer = data.get('customer', {}) or {}
            if isinstance(customer, dict):
                base_info['customer_name'] = customer.get('name', '')
                base_info['customer_address'] = customer.get('address', '')
                base_info['customer_phone'] = customer.get('phone', '')
                base_info['customer_email'] = customer.get('email', '')

            # Extract tax info
            tax_info = data.get('tax_info', {}) or {}
            if isinstance(tax_info, dict):
                base_info['tax_type'] = tax_info.get('tax_type', '')
                base_info['tax_rate'] = tax_info.get('tax_rate', 0)
                base_info['tax_amount'] = tax_info.get('tax_amount', 0)
            else:
                base_info['tax_amount'] = data.get('tax_amount', 0)

            # Handle line items - create one row per item like sample.csv
            items = data.get('items', []) or []
            if not items or not isinstance(items, list):
                # No items, write single row with base info - ensure no None values
                clean_values = []
                for value in base_info.values():
                    if value is None:
                        clean_values.append('')
                    else:
                        clean_values.append(str(value))
                writer.writerow(clean_values)
            else:
                # Write one row per item (like sample.csv)
                for item in items:
                    row = base_info.copy()
                    if isinstance(item, dict):
                        row['item_name'] = item.get('name', '')
                        row['item_category'] = item.get('category', '')
                        row['quantity'] = item.get('quantity', 1)
                        row['unit_price'] = item.get('unit_price', 0)
                        row['item_total'] = item.get('total_price', 0)
                    # Ensure no None values that could create gaps
                    clean_values = []
                    for value in row.values():
                        if value is None:
                            clean_values.append('')
                        else:
                            clean_values.append(str(value))
                    writer.writerow(clean_values)

            # Get the content and remove any extra newlines
            content = output.getvalue()
            # Remove any double newlines that might create gaps
            content = content.replace('\n\n', '\n')
            # Remove trailing newline to prevent empty line at end
            content = content.rstrip('\n')
            return content

        except Exception as e:
            logger.error(f"Error generating enhanced handwritten CSV content: {e}")
            return f"filename,error\n{filename},Error generating CSV: {str(e)}\n"
