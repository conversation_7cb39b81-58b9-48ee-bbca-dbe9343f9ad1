"""
FastAPI Application for Handwritten Mode Document Processing

This module provides a FastAPI-based web service for processing handwritten documents
and complex images using advanced OCR technology and Groq vision models. It specializes
in extracting structured data from handwritten invoices, forms, and notes.

Key Features:
- Advanced handwriting recognition using Groq vision models
- Image preprocessing and enhancement for better OCR accuracy
- Section-based processing for large documents
- Multi-pass recognition with confidence scoring
- Support for various image formats (PNG, JPG, JPEG, TIFF, etc.)
- Intelligent document area segmentation
- Quality assessment and recommendations
- Comprehensive error handling and logging

Architecture:
- Receives image files from Bridge API or direct clients
- Preprocesses images for optimal OCR performance
- Uses Groq vision models for handwriting recognition
- Extracts structured data from recognized text
- Returns standardized CSV format with document data
- Provides file management and processing status
"""

# Standard library imports for core functionality
import os  # Operating system interface for environment variables
import json  # JSON data handling for configuration and responses
import shutil  # High-level file operations for file management
import tempfile  # Temporary file handling for image processing
import csv  # CSV file operations for data output
import io  # Input/output operations for in-memory file handling
import sys  # System-specific parameters and functions
import logging  # Logging framework for debugging and monitoring
from datetime import datetime  # Date/time operations for timestamps
from pathlib import Path  # Modern path handling utilities
from typing import List, Optional, Dict, Any  # Type hints for better code clarity
from io import BytesIO  # In-memory binary streams for file handling

# FastAPI framework imports for web API functionality
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks, Query, Request  # Core FastAPI components
from fastapi.responses import JSONResponse, FileResponse, Response  # Response types
from fastapi.middleware.cors import CORSMiddleware  # Cross-origin resource sharing
from pydantic import BaseModel  # Data validation and serialization
import uvicorn  # ASGI server for running the FastAPI application
from PIL import Image  # Python Imaging Library for image processing

# Local module imports for application functionality
from handwritten import HandwrittenOCR  # Core handwriting recognition engine
from data_models import StructuredDocument  # Pydantic models for data validation
from config import get_settings  # Configuration management

# Add parent directory to path for shared logging utilities
sys.path.append(str(Path(__file__).parent.parent))
from shared_logging import get_logger


settings = get_settings()

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize processing logger
processing_logger = get_logger("handwritten-mode")



class OCRResult(BaseModel):
    """OCR processing result"""
    filename: str
    processed_at: str
    image_size: List[int]
    sections_processed: int
    extracted_text: str
    invoice_data: Dict[str, Any]
    status: str


class ProcessingStatus(BaseModel):
    """Processing status response"""
    task_id: str
    status: str  
    message: Optional[str] = None
    result: Optional[OCRResult] = None


class FileInfo(BaseModel):
    """File information"""
    filename: str
    size: int
    uploaded_at: str
    processed: bool
    result_available: bool


class APIResponse(BaseModel):
    """Standard API response"""
    success: bool
    message: str
    data: Optional[Any] = None



app = FastAPI(**settings.get_app_config())


app.add_middleware(
    CORSMiddleware,
    **settings.get_cors_config()
)


ocr_processor = None


processing_tasks: Dict[str, Dict] = {}


@app.on_event("startup")
async def startup_event():
    """Initialize the OCR processor on startup"""
    global ocr_processor
    try:
        
        settings.create_directories()

        
        ocr_processor = HandwrittenOCR()

        
        settings.print_config()
        print("SUCCESS: OCR processor initialized successfully")
    except Exception as e:
        print(f"ERROR: Failed to initialize OCR processor: {e}")
        raise


@app.get("/", response_model=APIResponse)
async def root():
    """Root endpoint with API information"""
    return APIResponse(
        success=True,
        message=f"{settings.APP_NAME} is running",
        data={
            "name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "description": settings.APP_DESCRIPTION,
            "endpoints": {
                "upload": "/upload",
                "process": "/process/{filename}",
                "process_csv": "/process-document/csv",
                "results": "/results/{filename}",
                "files": "/files",
                "health": "/health"
            },
            "limits": {
                "max_file_size_mb": settings.MAX_FILE_SIZE_MB,
                "max_batch_files": settings.MAX_BATCH_FILES,
                "allowed_extensions": settings.ALLOWED_EXTENSIONS
            }
        }
    )


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "ocr_ready": ocr_processor is not None
    }


@app.post("/upload", response_model=APIResponse)
async def upload_file(file: UploadFile = File(...)):
    """Upload an image file for OCR processing"""
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type. Allowed: {', '.join(settings.ALLOWED_EXTENSIONS)}"
        )

    
    file_content = await file.read()
    if len(file_content) > settings.MAX_FILE_SIZE_BYTES:
        raise HTTPException(
            status_code=413,
            detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE_MB}MB"
        )

    try:
        
        input_dir = Path(settings.INPUT_DIR)
        input_dir.mkdir(exist_ok=True)

        
        file_path = input_dir / file.filename
        with open(file_path, "wb") as buffer:
            buffer.write(file_content)

        
        file_size = file_path.stat().st_size

        return APIResponse(
            success=True,
            message=f"File '{file.filename}' uploaded successfully",
            data={
                "filename": file.filename,
                "size": file_size,
                "size_mb": round(file_size / (1024 * 1024), 2),
                "uploaded_at": datetime.now().isoformat(),
                "path": str(file_path)
            }
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")


@app.post("/process/{filename}", response_model=APIResponse)
async def process_image(filename: str, background_tasks: BackgroundTasks):
    """Process an uploaded image with OCR"""
    input_path = Path(settings.INPUT_DIR) / filename

    if not input_path.exists():
        raise HTTPException(status_code=404, detail=f"File '{filename}' not found")

    if not ocr_processor:
        raise HTTPException(status_code=500, detail="OCR processor not initialized")

    
    active_tasks = sum(1 for task in processing_tasks.values()
                      if task.get("status") in ["pending", "processing"])
    if active_tasks >= settings.MAX_CONCURRENT_TASKS:
        raise HTTPException(
            status_code=429,
            detail=f"Too many concurrent tasks. Maximum: {settings.MAX_CONCURRENT_TASKS}"
        )

    
    task_id = f"{filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    
    processing_tasks[task_id] = {
        "status": "pending",
        "filename": filename,
        "started_at": datetime.now().isoformat(),
        "result": None
    }

    
    background_tasks.add_task(process_image_background, task_id, input_path)

    return APIResponse(
        success=True,
        message=f"Processing started for '{filename}'",
        data={
            "task_id": task_id,
            "status": "pending",
            "check_status_url": f"/status/{task_id}",
            "estimated_time": "30-120 seconds"
        }
    )


async def process_image_background(task_id: str, image_path: Path):
    """Background task for processing images"""
    try:
        processing_tasks[task_id]["status"] = "processing"
        
        
        result = ocr_processor.process_single_image(image_path)
        
        
        processing_tasks[task_id].update({
            "status": "completed",
            "completed_at": datetime.now().isoformat(),
            "result": result
        })
        
    except Exception as e:
        processing_tasks[task_id].update({
            "status": "failed",
            "error": str(e),
            "completed_at": datetime.now().isoformat()
        })


@app.get("/status/{task_id}", response_model=ProcessingStatus)
async def get_processing_status(task_id: str):
    """Get the status of a processing task"""
    if task_id not in processing_tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task = processing_tasks[task_id]
    
    return ProcessingStatus(
        task_id=task_id,
        status=task["status"],
        message=task.get("error"),
        result=OCRResult(**task["result"]) if task.get("result") else None
    )


@app.get("/results/{filename}", response_model=APIResponse)
async def get_results(filename: str):
    """Get OCR results for a processed file"""
    
    output_dir = Path(settings.OUTPUT_DIR)
    json_files = list(output_dir.glob(f"ocr_results_*.json"))

    if not json_files:
        raise HTTPException(status_code=404, detail="No results found")

    
    latest_file = max(json_files, key=lambda x: x.stat().st_mtime)

    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            results = json.load(f)

        
        file_result = None
        for result in results:
            if result.get("filename") == filename:
                file_result = result
                break

        if not file_result:
            raise HTTPException(status_code=404, detail=f"No results found for '{filename}'")

        return APIResponse(
            success=True,
            message=f"Results retrieved for '{filename}'",
            data=file_result
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve results: {str(e)}")


@app.get("/files", response_model=APIResponse)
async def list_files():
    """List all uploaded files and their processing status"""
    input_dir = Path(settings.INPUT_DIR)
    output_dir = Path(settings.OUTPUT_DIR)

    if not input_dir.exists():
        return APIResponse(
            success=True,
            message="No files uploaded yet",
            data=[]
        )

    
    files_info = []
    for file_path in input_dir.iterdir():
        if file_path.is_file():
            
            result_exists = False
            if output_dir.exists():
                json_files = list(output_dir.glob("ocr_results_*.json"))
                for json_file in json_files:
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            results = json.load(f)
                        for result in results:
                            if result.get("filename") == file_path.name:
                                result_exists = True
                                break
                        if result_exists:
                            break
                    except:
                        continue

            file_info = FileInfo(
                filename=file_path.name,
                size=file_path.stat().st_size,
                uploaded_at=datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                processed=result_exists,
                result_available=result_exists
            )
            files_info.append(file_info)

    return APIResponse(
        success=True,
        message=f"Found {len(files_info)} files",
        data=files_info
    )


@app.delete("/files/{filename}", response_model=APIResponse)
async def delete_file(filename: str):
    """Delete an uploaded file"""
    input_path = Path(settings.INPUT_DIR) / filename

    if not input_path.exists():
        raise HTTPException(status_code=404, detail=f"File '{filename}' not found")

    try:
        input_path.unlink()
        return APIResponse(
            success=True,
            message=f"File '{filename}' deleted successfully"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete file: {str(e)}")


@app.get("/download/{filename}")
async def download_file(filename: str, file_type: str = Query("image", description="Type: image, json, csv")):
    """Download original image or processed results"""
    if file_type == "image":
        file_path = Path(settings.INPUT_DIR) / filename
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"Image '{filename}' not found")
        return FileResponse(file_path, filename=filename)

    elif file_type == "json":
        output_dir = Path(settings.OUTPUT_DIR)
        json_files = list(output_dir.glob("ocr_results_*.json"))
        if not json_files:
            raise HTTPException(status_code=404, detail="No JSON results found")
        latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
        return FileResponse(latest_file, filename=f"ocr_results_{filename}.json")

    elif file_type == "csv":
        output_dir = Path(settings.OUTPUT_DIR)
        csv_files = list(output_dir.glob("invoice_data_*.csv"))
        if not csv_files:
            raise HTTPException(status_code=404, detail="No CSV results found")
        latest_file = max(csv_files, key=lambda x: x.stat().st_mtime)
        return FileResponse(latest_file, filename=f"invoice_data_{filename}.csv")

    else:
        raise HTTPException(status_code=400, detail="Invalid file_type. Use: image, json, or csv")


@app.post("/process-document/csv")
async def process_document_csv(file: UploadFile = File(...)):
    """Process a document and directly return CSV results"""
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type. Allowed: {', '.join(settings.ALLOWED_EXTENSIONS)}"
        )

    
    file_content = await file.read()
    if len(file_content) > settings.MAX_FILE_SIZE_BYTES:
        raise HTTPException(
            status_code=413,
            detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE_MB}MB"
        )

    if not ocr_processor:
        raise HTTPException(status_code=500, detail="OCR processor not initialized")

    try:
        
        with tempfile.NamedTemporaryFile(suffix=file_ext, delete=False) as temp_file:
            temp_file.write(file_content)
            temp_path = Path(temp_file.name)

        try:
            
            result = ocr_processor.process_single_image(temp_path)

            if result["status"] != "success":
                raise HTTPException(status_code=500, detail="OCR processing failed")

            # Create CSV content in simplified format
            csv_buffer = io.StringIO()

            # Extract invoice data
            invoice_data = result.get("invoice_data", {})

            # Define the simplified CSV column structure
            csv_columns = [
                "invoice_number", "invoice_date", "due_date", "purchase_order_number",
                "vendor_name", "vendor_address", "vendor_phone", "vendor_email",
                "customer_name", "customer_address", "customer_phone", "customer_email",
                "currency", "subtotal", "tax_type", "tax_rate", "tax_amount", "discount_amount",
                "shipping_amount", "total", "payment_terms", "payment_method", "notes", "other_information",
                "item_name", "item_category", "quantity", "unit_price", "item_total"
            ]

            # Create CSV writer with tab delimiter
            writer = csv.writer(csv_buffer, delimiter='\t')

            # Write header row
            writer.writerow(csv_columns)

            # Extract items from the invoice data
            items = []

            # Check for individual item fields (item_1, item_2, item_3)
            for i in range(1, 4):
                item_name = invoice_data.get(f"item_{i}_name", "")
                if item_name and item_name.strip():
                    items.append({
                        "name": item_name,
                        "category": invoice_data.get(f"item_{i}_category", ""),
                        "quantity": invoice_data.get(f"item_{i}_quantity", 1),
                        "unit_price": invoice_data.get(f"item_{i}_unit_price", 0),
                        "total": invoice_data.get(f"item_{i}_total", 0)
                    })

            # If no items found, create one empty row
            if not items:
                items = [{"name": "", "category": "", "quantity": "", "unit_price": "", "total": ""}]

            # Write data rows (one row per item)
            for item in items:
                data_row = []

                # Add document-level fields
                document_fields = [
                    "invoice_number", "invoice_date", "due_date", "purchase_order_number",
                    "vendor_name", "vendor_address", "vendor_phone", "vendor_email",
                    "customer_name", "customer_address", "customer_phone", "customer_email",
                    "currency", "subtotal", "tax_type", "tax_rate", "tax_amount", "discount_amount",
                    "shipping_amount", "total", "payment_terms", "payment_method", "notes", "other_information"
                ]

                for field in document_fields:
                    value = invoice_data.get(field, "")
                    if value is None:
                        value = ""
                    elif isinstance(value, (int, float)):
                        value = str(value)
                    else:
                        value = str(value)
                    data_row.append(value)

                # Add item-specific fields
                data_row.append(str(item["name"]))
                data_row.append(str(item["category"]))
                data_row.append(str(item["quantity"]))
                data_row.append(str(item["unit_price"]))
                data_row.append(str(item["total"]))

                writer.writerow(data_row)

            csv_content = csv_buffer.getvalue()
            csv_buffer.close()

            
            return Response(
                content=csv_content,
                media_type="text/csv",
                headers={
                    "Content-Disposition": f"attachment; filename={Path(file.filename).stem}_results.csv"
                }
            )

        finally:
            
            if temp_path.exists():
                temp_path.unlink()

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")


@app.post("/process-all", response_model=APIResponse)
async def process_all_images(background_tasks: BackgroundTasks):
    """Process all uploaded images"""
    input_dir = Path(settings.INPUT_DIR)

    if not input_dir.exists() or not any(input_dir.iterdir()):
        raise HTTPException(status_code=404, detail="No images found to process")

    if not ocr_processor:
        raise HTTPException(status_code=500, detail="OCR processor not initialized")

    
    file_count = sum(1 for f in input_dir.iterdir() if f.is_file())
    if file_count > settings.MAX_BATCH_FILES:
        raise HTTPException(
            status_code=413,
            detail=f"Too many files. Maximum batch size: {settings.MAX_BATCH_FILES}"
        )

    
    batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    
    processing_tasks[batch_id] = {
        "status": "pending",
        "type": "batch",
        "started_at": datetime.now().isoformat(),
        "files": [f.name for f in input_dir.iterdir() if f.is_file()],
        "results": []
    }

    
    background_tasks.add_task(process_all_images_background, batch_id)

    return APIResponse(
        success=True,
        message="Batch processing started for all images",
        data={
            "batch_id": batch_id,
            "status": "pending",
            "check_status_url": f"/status/{batch_id}"
        }
    )


async def process_all_images_background(batch_id: str):
    """Background task for processing all images"""
    try:
        processing_tasks[batch_id]["status"] = "processing"

        
        results = ocr_processor.process_all_images()

        
        ocr_processor.save_results(results)

        
        processing_tasks[batch_id].update({
            "status": "completed",
            "completed_at": datetime.now().isoformat(),
            "results": results
        })

    except Exception as e:
        processing_tasks[batch_id].update({
            "status": "failed",
            "error": str(e),
            "completed_at": datetime.now().isoformat()
        })


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        **settings.get_uvicorn_config()
    )
