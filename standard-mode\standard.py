import os
import sys
import argparse
import logging
import csv
import json
from pathlib import Path
from typing import List, Optional
import time


try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("error")


try:
    from parser import DocumentParser
    from models import DocumentParsingResult
    ENHANCED_PARSER_AVAILABLE = True
except ImportError as e:
    print(f" parser not available: {e}")
    ENHANCED_PARSER_AVAILABLE = False


logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def setup_argument_parser() -> argparse.ArgumentParser:
    description = """
Enhanced Invoice/Receipt Document Parser with Multi-Language Support

PROCESSING MODES:
  Standard Mode:    Regular document processing with OCR and auto-language detection
  --img:           Enhanced image processing mode (same as standard for now)
  --disable-vision: Use OCR only (same as standard mode)

LANGUAGE SUPPORT:
  --language:      Specify document language (e.g., en, es, fr, de, zh, ja, ar, hi)
  --auto-detect:   Enable automatic language detection (default: enabled)
  --no-auto-detect: Disable automatic language detection

EXAMPLES:
  
  python 2test.py -i invoice.pdf -o results.csv

  
  python 2test.py -i factura.pdf -o results.csv --language es

  
  python 2test.py -i 发票.pdf -o results.csv --language zh

  
  python 2test.py -i document.png -o results.csv --img

  
  python 2test.py -i ./invoices/ -o batch_results.csv --batch

  
  python 2test.py -i invoice.pdf -o results.csv --save-json

  
  python 2test.py -i rechnung.pdf -o results.csv --language de --no-auto-detect

SUPPORTED LANGUAGES (80+):
  European: en, es, fr, de, it, pt, ru, pl, nl, sv, da, no, fi, cs, sk, hu, ro, bg, hr, sr, sl, et, lv, lt, uk, be, mk, sq, eu, ca, gl, cy, ga, mt, is, fo
  Asian: zh, ja, ko, th, vi, hi, bn, gu, pa, ta, te, kn, ml, si, my, km, lo, mn, ne, mr, or, as
  Middle Eastern: ar, he, fa, ur, ku, ps, am, ti
  African: sw, zu, xh, af, yo, ig, ha, om, so, mg, ny, sn, st, tn, ve, ts, ss, nr
  Others: ms, id, tl, ceb, jv, su

SUPPORTED FORMATS:
  Images: PNG, JPG, JPEG, TIFF, BMP, WEBP, GIF
  Documents: PDF
  Text: TXT
"""

    parser = argparse.ArgumentParser(
        description=description,
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )
    
    parser.add_argument(
        '--input', '-i',
        type=str,
        required=True,
        help='Input file or directory path'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=str,
        required=True,
        help='Output CSV file path'
    )
    
    parser.add_argument(
        '--batch', '-b',
        action='store_true',
        help='Process all files in input directory (batch mode)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--groq-api-key',
        type=str,
        help='Groq API key (can also be set via GROQ_API_KEY environment variable)'
    )
    
    parser.add_argument(
        '--save-json',
        action='store_true',
        help='Also save results in JSON format'
    )
    
    parser.add_argument(
        '--save-raw-text',
        action='store_true',
        help='Save extracted raw text'
    )



    parser.add_argument(
        '--img',
        action='store_true',
        help='Enhanced image processing mode (currently same as standard mode)'
    )

    parser.add_argument(
        '--disable-vision',
        action='store_true',
        help='Use OCR only mode (currently same as standard mode)'
    )

    parser.add_argument(
        '--language', '-l',
        type=str,
        help='Specify document language code (e.g., en, es, fr, de, zh, ja, ar, hi). See help for full list of 80+ supported languages.'
    )

    parser.add_argument(
        '--auto-detect',
        action='store_true',
        default=True,
        help='Enable automatic language detection (default: enabled)'
    )

    parser.add_argument(
        '--no-auto-detect',
        action='store_true',
        help='Disable automatic language detection'
    )

    parser.add_argument(
        '--show-languages',
        action='store_true',
        help='Show all supported languages and exit'
    )

    return parser


def show_supported_languages():
    """Display all supported languages and their codes."""
    print("\n🌍 SUPPORTED LANGUAGES (80+)")
    print("=" * 60)

    languages = {
        "European Languages": {
            'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German', 'it': 'Italian',
            'pt': 'Portuguese', 'ru': 'Russian', 'pl': 'Polish', 'nl': 'Dutch', 'sv': 'Swedish',
            'da': 'Danish', 'no': 'Norwegian', 'fi': 'Finnish', 'cs': 'Czech', 'sk': 'Slovak',
            'hu': 'Hungarian', 'ro': 'Romanian', 'bg': 'Bulgarian', 'hr': 'Croatian', 'sr': 'Serbian',
            'sl': 'Slovenian', 'et': 'Estonian', 'lv': 'Latvian', 'lt': 'Lithuanian', 'uk': 'Ukrainian',
            'be': 'Belarusian', 'mk': 'Macedonian', 'sq': 'Albanian', 'eu': 'Basque', 'ca': 'Catalan',
            'gl': 'Galician', 'cy': 'Welsh', 'ga': 'Irish', 'mt': 'Maltese', 'is': 'Icelandic', 'fo': 'Faroese'
        },
        "Asian Languages": {
            'zh': 'Chinese', 'ja': 'Japanese', 'ko': 'Korean', 'th': 'Thai', 'vi': 'Vietnamese',
            'hi': 'Hindi', 'bn': 'Bengali', 'gu': 'Gujarati', 'pa': 'Punjabi', 'ta': 'Tamil',
            'te': 'Telugu', 'kn': 'Kannada', 'ml': 'Malayalam', 'si': 'Sinhala', 'my': 'Myanmar',
            'km': 'Khmer', 'lo': 'Lao', 'mn': 'Mongolian', 'ne': 'Nepali', 'mr': 'Marathi',
            'or': 'Odia', 'as': 'Assamese'
        },
        "Middle Eastern & Central Asian": {
            'ar': 'Arabic', 'he': 'Hebrew', 'fa': 'Persian/Farsi', 'ur': 'Urdu', 'ku': 'Kurdish',
            'ps': 'Pashto', 'am': 'Amharic', 'ti': 'Tigrinya', 'ka': 'Georgian', 'hy': 'Armenian',
            'az': 'Azerbaijani', 'kk': 'Kazakh', 'ky': 'Kyrgyz', 'uz': 'Uzbek', 'tg': 'Tajik'
        },
        "African Languages": {
            'sw': 'Swahili', 'zu': 'Zulu', 'xh': 'Xhosa', 'af': 'Afrikaans', 'yo': 'Yoruba',
            'ig': 'Igbo', 'ha': 'Hausa', 'om': 'Oromo', 'so': 'Somali', 'mg': 'Malagasy',
            'ny': 'Chichewa', 'sn': 'Shona', 'st': 'Sesotho', 'tn': 'Tswana', 've': 'Venda',
            'ts': 'Tsonga', 'ss': 'Swati', 'nr': 'Ndebele'
        },
        "Southeast Asian & Pacific": {
            'ms': 'Malay', 'id': 'Indonesian', 'tl': 'Filipino/Tagalog', 'ceb': 'Cebuano',
            'jv': 'Javanese', 'su': 'Sundanese'
        }
    }

    for category, langs in languages.items():
        print(f"\n📍 {category}:")
        for code, name in langs.items():
            print(f"   {code:3} - {name}")



def find_files_to_process(input_path: Path, batch_mode: bool) -> List[Path]:
    supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.txt', '.webp', '.gif'}
    
    if batch_mode:
        if not input_path.is_dir():
            raise ValueError(f"Batch mode requires a directory, got: {input_path}")
        
        files = []
        for ext in supported_extensions:
            files.extend(input_path.glob(f"*{ext}"))
            files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not files:
            raise ValueError(f"No supported files found in directory: {input_path}")
        
        return sorted(files)
    else:
        if not input_path.exists():
            raise ValueError(f"Input file not found: {input_path}")
        
        if input_path.suffix.lower() not in supported_extensions:
            raise ValueError(f"Unsupported file type: {input_path.suffix}")
        
        return [input_path]


def process_with_image_mode(doc_parser: DocumentParser, file_path: Path, language_hint: Optional[str] = None) -> DocumentParsingResult:
    """Enhanced image processing mode with multi-language support."""
    logger.info(f"Processing {file_path} with enhanced image mode")

    result = doc_parser.parse_document(file_path, language_hint=language_hint)

    if result.success and result.invoice_data:
        logger.info("✓ Enhanced image processing successful")
    else:
        logger.warning("Image mode processing had issues, result may be incomplete")

    return result


def save_results_to_csv(results: List[DocumentParsingResult], output_path: Path) -> None:
    csv_rows = []

    for result in results:
        if result.success and result.invoice_data:
           
            invoice_rows = result.invoice_data.to_csv_rows()
            csv_rows.extend(invoice_rows)
        else:
           
            error_row = {
                'invoice_number': '',
                'invoice_date': '',
                'due_date': '',
                'purchase_order_number': '',
                'vendor_name': '',
                'vendor_address': '',
                'vendor_phone': '',
                'vendor_email': '',
                'customer_name': '',
                'customer_address': '',
                'customer_phone': '',
                'customer_email': '',
                'currency': 'USD',
                'subtotal': 0,
                'tax_type': '',
                'tax_rate': 0,
                'tax_amount': 0,
                'discount_amount': 0,
                'shipping_amount': 0,
                'total': 0,
                'payment_terms': '',
                'payment_method': '',
                'notes': '',
                'item_name': f"ERROR: {result.error_message}",
                'item_category': '',
                'quantity': 0,
                'unit_price': 0,
                'item_total': 0,
                'other_information': ''
            }
            csv_rows.append(error_row)
    
    if not csv_rows:
        logger.warning("No data to save to CSV")
        return
    
    
    with open(output_path, 'w', newline='', encoding='utf-8') as f:
        if csv_rows:
            writer = csv.DictWriter(f, fieldnames=csv_rows[0].keys())
            writer.writeheader()
            writer.writerows(csv_rows)
    
    logger.info(f"Results saved to: {output_path}")


def save_results_to_json(results: List[DocumentParsingResult], output_path: Path) -> None:
    json_data = []
    
    for result in results:
        result_dict = {
            'success': result.success,
            'error_message': result.error_message,
            'processing_time': result.processing_time,
            'extraction_method': result.extraction_method,
            'invoice_data': result.invoice_data.model_dump() if result.invoice_data else None
        }
        json_data.append(result_dict)
    
    json_path = output_path.with_suffix('.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)
    
    logger.info(f"JSON results saved to: {json_path}")


def save_raw_text(results: List[DocumentParsingResult], output_path: Path) -> None:
    for i, result in enumerate(results):
        if result.raw_text:
            text_path = output_path.parent / f"{output_path.stem}_raw_text_{i}.txt"
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(result.raw_text)
            logger.info(f"Raw text saved to: {text_path}")


def main():
    parser = setup_argument_parser()
    args = parser.parse_args()

    if args.show_languages:
        show_supported_languages()
        sys.exit(0)

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)


    if not ENHANCED_PARSER_AVAILABLE:
        logger.error("Enhanced parser not available. Please install required dependencies.")
        sys.exit(1)


    groq_api_key = args.groq_api_key or os.getenv("GROQ_API_KEY")
    if not groq_api_key:
        logger.error("GROQ_API_KEY not found. Please set it as environment variable or use --groq-api-key")
        sys.exit(1)

    auto_detect_language = args.auto_detect and not args.no_auto_detect
    language_hint = args.language

    if language_hint:
        logger.info(f"🌍 Language hint provided: {language_hint}")
    if auto_detect_language:
        logger.info("🔍 Automatic language detection enabled")
    else:
        logger.info("🔍 Automatic language detection disabled")
    
    try:
        
        logger.info(f"Initializing parser with auto_detect_language={auto_detect_language}")
        doc_parser = DocumentParser(
            groq_api_key=groq_api_key,
            auto_detect_language=auto_detect_language
        )

       
        input_path = Path(args.input)
        files_to_process = find_files_to_process(input_path, args.batch)

        logger.info(f"Found {len(files_to_process)} file(s) to process")

        if args.img:
            logger.info(" IMAGE MODE: Enhanced image processing")
        else:
            logger.info(" STANDARD MODE: Using OCR for text extraction with automatic translation")

        if language_hint:
            logger.info(f" Processing with language hint: {language_hint}")

        start_time = time.time()
        if args.batch:
            for file_path in files_to_process:
                result = doc_parser.parse_document(file_path, language_hint=language_hint)
                results.append(result)
        else:
            if args.img:
                result = process_with_image_mode(doc_parser, files_to_process[0], language_hint)
            else:
                result = doc_parser.parse_document(files_to_process[0], language_hint=language_hint)
            results = [result]
        
        total_time = time.time() - start_time
        
        
        
        
        output_path = Path(args.output)
        save_results_to_csv(results, output_path)
        
        if args.save_json:
            save_results_to_json(results, output_path)
        
        if args.save_raw_text:
            save_raw_text(results, output_path)
        

        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful
        processing_mode = "Standard"
        if args.img:
            processing_mode = "Enhanced Image"
        else:
            processing_mode = "Standard OCR with Auto-Translation"
        if language_hint:
            processing_mode += f" (Language: {language_hint})"
        elif auto_detect_language:
            processing_mode += " (Auto-detect language)"

        print(f"\n{'='*60}")
        print(f"PROCESSING COMPLETE")
        print(f"{'='*60}")
        print(f"Processing Mode: {processing_mode}")
        print(f"Total files processed: {len(results)}")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        print(f"Total processing time: {total_time:.2f} seconds")
        print(f"Average time per file: {total_time/len(results):.2f} seconds")
        print(f"Results saved to: {output_path}")
        if auto_detect_language and successful > 0:
            print(f"\n Language Detection Summary:")
            detected_languages = set()
            for result in results:
                if result.success and hasattr(result, 'extraction_method') and 'multilingual' in str(result.extraction_method):
                    detected_languages.add("Multi-language processing used")
            if detected_languages:
                for lang_info in detected_languages:
                    print(f"  - {lang_info}")
            else:
                print(f"  - Language detection enabled for enhanced accuracy")
        extraction_methods = {}
        for result in results:
            if result.success and result.extraction_method:
                method = result.extraction_method
                extraction_methods[method] = extraction_methods.get(method, 0) + 1

        if extraction_methods:
            print(f"\nExtraction Methods Used:")
            for method, count in extraction_methods.items():
                print(f"  - {method}: {count} files")

        if failed > 0:
            print(f"\nFailed files:")
            for i, result in enumerate(results):
                if not result.success:
                    file_path = files_to_process[i] if i < len(files_to_process) else "unknown"
                    print(f"  - {file_path}: {result.error_message}")

        if args.img and successful > 0:
            print(f"\n  Image Processing Summary:")
            print(f"  - Enhanced image processing mode used")
            print(f"  - Optimized for image-based documents")
            print(f"  - Standard OCR with image optimizations")

        if successful > 0:
            print(f"\n Multi-Language Processing Summary:")
            print(f"  - Automatic language detection enabled")
            print(f"  - Non-English documents automatically translated to English")
            print(f"  - Supports 80+ languages with intelligent OCR configuration")
            print(f"  - Final output always in English for consistent processing")
    
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
