import { useState, useRef, useEffect } from "react";
import ChatMainUi from "./chatBotComponents/ChatMainUi";
import { ChartNoAxesColumnIcon, CrossIcon, PanelRightClose } from "lucide-react";
import GlassEffect from './GlassEffect';

const FloatingChat = ({ isChatVisible, setIsChatVisible }) => {
  const toggleChat = () => setIsChatVisible((prev) => !prev);

  return (
    <div className="fixed bottom-0 right-0 z-50">
      {/* Floating Chat Button */}
      <GlassEffect
        variant="button"
        onClick={toggleChat}
        className={`${isChatVisible ? 'opacity-0 pointer-events-none' : 'opacity-100'}
                   fixed bottom-6 right-6 !rounded-full transition-all duration-300 shadow-lg`}
        style={{ zIndex: 10001 }}
      >
        <div className="flex items-center gap-2 px-4 py-3">
          <span className="text-lg">💬</span>
          <span className="font-medium">Chat</span>
        </div>
      </GlassEffect>

      {/* Chat Window */}
      {isChatVisible && (
        <div
          className="fixed bottom-6 right-6 w-96 h-[500px] transition-all duration-300 ease-in-out"
          style={{ zIndex: 10000 }}
        >
          <div className="relative w-full h-full">
            {/* Close Button */}
            <GlassEffect
              variant="button"
              onClick={toggleChat}
              className="absolute -top-2 -right-2 !rounded-full !p-2 shadow-lg"
              style={{ zIndex: 10002 }}
            >
              <PanelRightClose className="w-4 h-4" />
            </GlassEffect>

            {/* Chat Content */}
            <div className="w-full h-full">
              <ChatMainUi />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FloatingChat;















// import { useState, useRef, useEffect } from "react";
// import ChatMainUi from "./chatBotComponents/ChatMainUi";

// const FloatingChat = () => {
//   const [isChatVisible, setIsChatVisible] = useState(false);  // State to toggle chat visibility
//   const chatRef = useRef(null);  // Reference for the chat bubble
//   const buttonRef = useRef(null);  // Reference for the chat button

//   // Function to toggle chat visibility
//   const toggleChat = () => setIsChatVisible((prev) => !prev);

//   // Close chat when clicking outside of the chat bubble or button
//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (
//         chatRef.current && !chatRef.current.contains(event.target) && 
//         buttonRef.current && !buttonRef.current.contains(event.target)
//       ) {
//         setIsChatVisible(false);  // Close the chat if click is outside
//       }
//     };

//     // Add event listener for detecting clicks outside the component
//     document.addEventListener("mousedown", handleClickOutside);

//     // Cleanup the event listener when component unmounts
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, []);

//   return (
//     <div>
//       {/* Floating Chat Button */}
//       <button
//         ref={buttonRef}
//         onClick={toggleChat}
//         className={` ${isChatVisible?'opacity-0':'opacity-100'}
//             fixed bottom-[35px] right-[70px] p-4 py-5 bg-gradient-to-r from-pink-600 to-blue-700 text-white rounded-full shadow-lg transition-all duration-300 hover:scale-110`}
//         style={{ zIndex: 10000 }}
//       >
//         Chat
//       </button>

//       {/* ChatBubble (Floating) */}
//       {isChatVisible && (
//         <div
//           ref={chatRef}
//           className="fixed bottom-4 right-4 transition-all duration-500 ease-in-out transform"
//           style={{
//             zIndex: 9999,
//             opacity: isChatVisible ? 1 : 0,
//             transform: isChatVisible ? 'translateY(0)' : 'translateY(200px)',
//           }}
//         >
//           <ChatMainUi />
//         </div>
//       )}
//     </div>
//   );
// };

// export default FloatingChat;
