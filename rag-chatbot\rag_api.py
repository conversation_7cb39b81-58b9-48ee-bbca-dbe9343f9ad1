"""
RAG Chatbot API for Document Intelligence

This module provides a FastAPI-based conversational AI service that enables natural language
interaction with processed documents using Retrieval-Augmented Generation (RAG) technology.
It combines vector search, document retrieval, and LLM generation for intelligent responses.

Key Features:
- Natural language queries about document content
- Retrieval-Augmented Generation (RAG) pipeline using LangChain
- Vector similarity search with ChromaDB
- HuggingFace embeddings for semantic understanding
- Groq LLM for response generation
- Session-based conversation tracking
- Source document attribution
- Real-time database refresh capabilities
- Automatic document indexing from CSV files

Architecture:
- Monitors results directory for new CSV files
- Generates embeddings using HuggingFace transformers
- Stores vectors in ChromaDB for similarity search
- Processes user queries through RAG pipeline
- Retrieves relevant document context
- Generates contextual responses using Groq LLM
- Provides source attribution and confidence scoring
"""

# Standard library imports for core functionality
import os  # Operating system interface for environment variables
import sys  # System-specific parameters and functions
import json  # JSON data handling for responses and configuration
import logging  # Logging framework for debugging and monitoring
import uuid  # UUID generation for unique session identifiers
from pathlib import Path  # Modern path handling utilities
from typing import List, Dict, Any, Optional  # Type hints for better code clarity
from datetime import datetime  # Date/time operations for timestamps
import pandas as pd  # Data manipulation and analysis for CSV processing

# FastAPI framework imports for web API functionality
from fastapi import FastAPI, HTTPException, BackgroundTasks  # Core FastAPI components
from fastapi.middleware.cors import CORSMiddleware  # Cross-origin resource sharing
from pydantic import BaseModel, Field  # Data validation and serialization
import uvicorn  # ASGI server for running the FastAPI application

# LangChain imports for RAG pipeline implementation
from langchain_groq import ChatGroq  # Groq LLM integration for response generation
from langchain_huggingface import HuggingFaceEmbeddings  # Text embeddings using HuggingFace models
from langchain_chroma import Chroma  # Vector database for similarity search
from langchain.text_splitter import RecursiveCharacterTextSplitter  # Text chunking for embeddings
from langchain.schema import Document  # Document schema for text processing
from langchain.chains import RetrievalQA  # Question-answering chain with retrieval
from langchain.prompts import PromptTemplate  # Custom prompt templates for LLM

# Add parent directory for shared logging utilities
sys.path.append(str(Path(__file__).parent.parent))
from shared_logging import get_logger  # Custom logging utility for consistent log formatting

# Environment variable management
from dotenv import load_dotenv  # Load environment variables from .env files
current_dir = Path(__file__).parent
root_dir = current_dir.parent
env_file = root_dir / ".env"

if env_file.exists():
    load_dotenv(env_file)
    print(f"Loaded environment variables from: {env_file}")
else:
    print(f"WARNING: .env file not found at: {env_file}")
    # Try to load from current directory as fallback
    local_env = current_dir / ".env"
    if local_env.exists():
        load_dotenv(local_env)
        print(f"Loaded environment variables from: {local_env}")
    else:
        print("WARNING: No .env file found")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize processing logger
processing_logger = get_logger("rag-chatbot")

# Initialize FastAPI app
app = FastAPI(
    title="RAG Document Chatbot API",
    description="Chat with your processed documents using RAG (Retrieval-Augmented Generation)",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
class Config:
    def __init__(self):
        self.groq_api_key = os.getenv("GROQ_API_KEY")
        self.llm_model = "llama3-8b-8192"
        self.embedding_model = "sentence-transformers/all-mpnet-base-v2"
        self.vector_store_path = "./chroma_db"

        # Multiple possible locations for CSV files
        self.csv_search_dirs = [
            Path("../results"),           # Default results directory
            Path("./results"),            # Local results directory
            Path("../"),                  # Root directory
            Path("./"),                   # Current directory
            Path("../uploads"),           # Uploads directory
            Path("../downloads"),         # Downloads directory
        ]

        self.chunk_size = 1000
        self.chunk_overlap = 200
        self.max_retrieval_docs = 5

config = Config()

# Global variables
llm = None
embeddings = None
vector_store = None
qa_chain = None

# Pydantic models
class ChatMessage(BaseModel):
    message: str = Field(..., description="User's question about the documents")
    session_id: Optional[str] = Field(None, description="Optional session ID for conversation tracking")

class ChatResponse(BaseModel):
    response: str = Field(..., description="Chatbot's response")
    session_id: str = Field(..., description="Session ID for conversation tracking")
    sources: List[str] = Field(..., description="Source documents used for the response")
    timestamp: str = Field(..., description="Response timestamp")

class DatabaseStatus(BaseModel):
    status: str
    document_count: int
    last_updated: str
    vector_store_size: int

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    llm_configured: bool
    embeddings_configured: bool
    vector_store_configured: bool
    documents_loaded: int

def initialize_components():
    """Initialize LLM, embeddings, and vector store"""
    global llm, embeddings, vector_store, qa_chain
    
    try:
        # Initialize Groq LLM
        if not config.groq_api_key:
            raise ValueError("GROQ_API_KEY environment variable not set")
        
        llm = ChatGroq(
            groq_api_key=config.groq_api_key,
            model_name=config.llm_model,
            temperature=0.1,
            max_tokens=1024
        )
        
        # Initialize HuggingFace embeddings
        embeddings = HuggingFaceEmbeddings(
            model_name=config.embedding_model,
            model_kwargs={'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True}
        )
        
        # Initialize ChromaDB vector store
        try:
            vector_store = Chroma(
                persist_directory=config.vector_store_path,
                embedding_function=embeddings,
                collection_name="document_collection"
            )
            logger.info("ChromaDB vector store initialized")
        except Exception as init_error:
            logger.error(f"Failed to initialize ChromaDB: {init_error}")
            raise RuntimeError(f"Failed to initialize ChromaDB: {init_error}")

        # Ensure collection is properly initialized
        try:
            # Try to get collection info to test if it's working
            collection_count = vector_store._collection.count()
            logger.info(f"Vector store initialized with {collection_count} documents")
        except Exception as e:
            logger.warning(f"Vector store collection needs initialization: {e}")
            # Reset and recreate the collection
            try:
                vector_store.reset_collection()
                logger.info("Vector store collection reset and recreated")
                collection_count = vector_store._collection.count()
                logger.info(f"After reset: {collection_count} documents")
            except Exception as reset_error:
                logger.error(f"Failed to reset collection: {reset_error}")
                # Create a new vector store instance
                try:
                    vector_store = Chroma(
                        persist_directory=config.vector_store_path,
                        embedding_function=embeddings,
                        collection_name="document_collection"
                    )
                    logger.info("Created new vector store instance")
                except Exception as recreate_error:
                    logger.error(f"Failed to recreate vector store: {recreate_error}")
                    raise RuntimeError(f"Failed to initialize vector store: {recreate_error}")
        
        # Create QA chain with custom prompt
        prompt_template = """
        You are a helpful assistant that answers questions about processed documents. 
        Use the following context from the documents to answer the question accurately.
        
        Context from documents:
        {context}
        
        Question: {question}
        
        Instructions:
        - Provide accurate answers based only on the information in the context
        - If the information is not in the context, say "I don't have that information in the processed documents"
        - Include specific details like amounts, dates, vendor names when available
        - Be concise but comprehensive
        - If referring to specific documents, mention the document name or type
        
        Answer:
        """
        
        PROMPT = PromptTemplate(
            template=prompt_template,
            input_variables=["context", "question"]
        )
        
        qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=vector_store.as_retriever(
                search_kwargs={"k": config.max_retrieval_docs}
            ),
            chain_type_kwargs={"prompt": PROMPT},
            return_source_documents=True
        )
        
        processing_logger.log_system_event(
            "components_initialized", 
            "RAG chatbot components initialized successfully"
        )
        
        return True
        
    except Exception as e:
        error_msg = f"Failed to initialize components: {str(e)}"
        logger.error(error_msg)
        processing_logger.log_error(None, None, "initialization_error", error_msg)
        return False

if __name__ == "__main__":
    uvicorn.run(
        "rag_api:app",
        host="0.0.0.0",
        port=8003,
        reload=True,
        log_level="info"
    )
