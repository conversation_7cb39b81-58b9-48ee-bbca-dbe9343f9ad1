"""
Shared Logging Utilities for Document Processing System

This module provides centralized logging functionality for all components of the
document processing system. It ensures consistent log formatting, structured
logging, and comprehensive event tracking across all services.

Key Features:
- Consistent log formatting across all components
- Structured logging with JSON support for analytics
- Event tracking for system monitoring
- Error logging with detailed context
- Performance metrics logging
- Processing event tracking
- Configurable log levels and outputs

Components:
- ProcessingLogger: Specialized logger for processing events
- get_logger: Factory function for creating consistent loggers
- Event tracking for system analytics and debugging
- Error context preservation for troubleshooting

Storage:
- Stores detailed processing logs in the processing_logs directory
- Separate log files for different components and event types
- JSON-structured logs for easy parsing and analysis
"""

# Standard library imports for logging functionality
import json  # JSON serialization for structured logs
import logging  # Core logging framework
import os  # Operating system interface for file operations
from datetime import datetime  # Timestamp generation for log entries
from pathlib import Path  # Path handling for log file management
from typing import Dict, Any, Optional  # Type hints for better code clarity
import uuid  # UUID generation for unique identifiers

class ProcessingLogger:
    """
    Specialized Logger for Document Processing Events

    This class provides structured logging specifically designed for document processing
    operations. It tracks processing events, errors, performance metrics, and system
    events with detailed context and metadata.

    Features:
    - Component-specific log files for organized logging
    - Structured JSON logging for analytics
    - Event categorization (processing, error, system, performance)
    - Automatic timestamp and metadata inclusion
    - Daily log rotation for better organization
    - Consistent formatting across all components

    Log Categories:
    - Processing Events: Document processing operations
    - Error Events: Errors with detailed context
    - System Events: System startup, shutdown, configuration
    - Performance Events: Timing and performance metrics
    """

    def __init__(self, server_name: str, log_dir: str = "processing_logs"):
        """
        Initialize the ProcessingLogger for a specific server/component.

        Args:
            server_name: Name of the server/component (e.g., 'bridge-api', 'standard-mode')
            log_dir: Directory for storing log files (default: 'processing_logs')
        """
        self.server_name = server_name  # Server identifier for log organization
        self.log_dir = Path(log_dir)  # Directory for storing log files
        self.log_dir.mkdir(exist_ok=True)  # Create logs directory if it doesn't exist

        # Create daily log file for better organization and rotation
        today = datetime.now().strftime('%Y%m%d')
        self.log_file = self.log_dir / f"processing_log_{today}.jsonl"

        # Setup standard logging with server-specific name
        self.logger = logging.getLogger(f"{server_name}_processing")
        self.logger.setLevel(logging.INFO)  # Set logging level

        # Create file handler if not exists to avoid duplicate handlers
        if not self.logger.handlers:
            handler = logging.FileHandler(self.log_file, encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def log_processing_start(self,
                           filename: str,
                           file_size: int,
                           file_type: str,
                           processing_mode: Optional[str] = None,
                           additional_data: Optional[Dict] = None) -> str:
        """
        Log the start of document processing operation.

        Creates a unique process ID and logs the initial processing event with
        file metadata and processing parameters.

        Args:
            filename: Name of the file being processed
            file_size: Size of the file in bytes
            file_type: Type of file (e.g., 'pdf', 'image')
            processing_mode: Processing mode ('standard' or 'handwritten')
            additional_data: Additional metadata to include in the log

        Returns:
            str: Unique process ID for tracking this processing operation
        """
        
        process_id = str(uuid.uuid4())
        
        log_entry = {
            "process_id": process_id,
            "server": self.server_name,
            "event": "processing_start",
            "timestamp": datetime.now().isoformat(),
            "filename": filename,
            "file_size": file_size,
            "file_type": file_type,
            "processing_mode": processing_mode,
            "additional_data": additional_data or {}
        }
        
        self._write_log_entry(log_entry)
        self.logger.info(f"Started processing {filename} (ID: {process_id})")
        
        return process_id
    
    def log_llm_decision(self, 
                        process_id: str,
                        filename: str,
                        decision: str,
                        confidence: float,
                        reasoning: str,
                        file_type: str):
        """Log LLM routing decision (Bridge API specific)"""
        
        log_entry = {
            "process_id": process_id,
            "server": self.server_name,
            "event": "llm_decision",
            "timestamp": datetime.now().isoformat(),
            "filename": filename,
            "file_type": file_type,
            "llm_decision": decision,
            "confidence": confidence,
            "reasoning": reasoning
        }
        
        self._write_log_entry(log_entry)
        self.logger.info(f"LLM decision for {filename}: {decision} (confidence: {confidence:.2f})")
    
    def log_backend_routing(self,
                           process_id: str,
                           filename: str,
                           target_backend: str,
                           backend_url: str,
                           fallback_enabled: bool):
        """Log backend routing information"""
        
        log_entry = {
            "process_id": process_id,
            "server": self.server_name,
            "event": "backend_routing",
            "timestamp": datetime.now().isoformat(),
            "filename": filename,
            "target_backend": target_backend,
            "backend_url": backend_url,
            "fallback_enabled": fallback_enabled
        }
        
        self._write_log_entry(log_entry)
        self.logger.info(f"Routing {filename} to {target_backend} backend")
    
    def log_fallback_triggered(self,
                              process_id: str,
                              filename: str,
                              original_backend: str,
                              fallback_backend: str,
                              error_reason: str):
        """Log when fallback processing is triggered"""
        
        log_entry = {
            "process_id": process_id,
            "server": self.server_name,
            "event": "fallback_triggered",
            "timestamp": datetime.now().isoformat(),
            "filename": filename,
            "original_backend": original_backend,
            "fallback_backend": fallback_backend,
            "error_reason": error_reason
        }
        
        self._write_log_entry(log_entry)
        self.logger.warning(f"Fallback triggered for {filename}: {original_backend} -> {fallback_backend}")
    
    def log_processing_complete(self,
                               process_id: str,
                               filename: str,
                               success: bool,
                               processing_time: float,
                               processing_mode: str,
                               extracted_fields: Optional[int] = None,
                               error_message: Optional[str] = None,
                               additional_data: Optional[Dict] = None):
        """Log completion of document processing"""
        
        log_entry = {
            "process_id": process_id,
            "server": self.server_name,
            "event": "processing_complete",
            "timestamp": datetime.now().isoformat(),
            "filename": filename,
            "success": success,
            "processing_time": processing_time,
            "processing_mode": processing_mode,
            "extracted_fields": extracted_fields,
            "error_message": error_message,
            "additional_data": additional_data or {}
        }
        
        self._write_log_entry(log_entry)
        
        if success:
            self.logger.info(f"Successfully processed {filename} in {processing_time:.2f}s (ID: {process_id})")
        else:
            self.logger.error(f"Failed to process {filename}: {error_message} (ID: {process_id})")
    
    def log_batch_processing(self,
                            batch_id: str,
                            total_files: int,
                            successful_files: int,
                            failed_files: int,
                            total_processing_time: float,
                            file_list: list):
        """Log batch processing summary"""
        
        log_entry = {
            "batch_id": batch_id,
            "server": self.server_name,
            "event": "batch_processing_complete",
            "timestamp": datetime.now().isoformat(),
            "total_files": total_files,
            "successful_files": successful_files,
            "failed_files": failed_files,
            "total_processing_time": total_processing_time,
            "success_rate": successful_files / total_files if total_files > 0 else 0,
            "file_list": file_list
        }
        
        self._write_log_entry(log_entry)
        self.logger.info(f"Batch processing complete: {successful_files}/{total_files} successful in {total_processing_time:.2f}s")
    
    def log_error(self,
                  process_id: Optional[str],
                  filename: Optional[str],
                  error_type: str,
                  error_message: str,
                  stack_trace: Optional[str] = None):
        """Log errors during processing"""
        
        log_entry = {
            "process_id": process_id,
            "server": self.server_name,
            "event": "error",
            "timestamp": datetime.now().isoformat(),
            "filename": filename,
            "error_type": error_type,
            "error_message": error_message,
            "stack_trace": stack_trace
        }
        
        self._write_log_entry(log_entry)
        self.logger.error(f"Error processing {filename or 'unknown'}: {error_message}")
    
    def log_system_event(self,
                        event_type: str,
                        message: str,
                        additional_data: Optional[Dict] = None):
        """Log system-level events (startup, shutdown, etc.)"""
        
        log_entry = {
            "server": self.server_name,
            "event": "system_event",
            "event_type": event_type,
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "additional_data": additional_data or {}
        }
        
        self._write_log_entry(log_entry)
        self.logger.info(f"System event: {event_type} - {message}")
    
    def _write_log_entry(self, log_entry: Dict[str, Any]):
        """Write a log entry to the JSONL file"""
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
        except Exception as e:
            # Fallback to standard logging if file write fails
            self.logger.error(f"Failed to write log entry: {e}")
    
    def get_processing_stats(self, days: int = 1) -> Dict[str, Any]:
        """Get processing statistics for the last N days"""
        stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "average_processing_time": 0,
            "by_server": {},
            "by_file_type": {},
            "by_processing_mode": {}
        }
        
        # This would read from log files and calculate stats
        # Implementation depends on specific requirements
        return stats

# Global logger instances for each server
bridge_logger = None
standard_logger = None
handwritten_logger = None

def get_logger(server_name: str) -> ProcessingLogger:
    """Get or create a logger for a specific server"""
    global bridge_logger, standard_logger, handwritten_logger
    
    if server_name == "bridge-api":
        if bridge_logger is None:
            bridge_logger = ProcessingLogger("bridge-api")
        return bridge_logger
    elif server_name == "standard-mode":
        if standard_logger is None:
            standard_logger = ProcessingLogger("standard-mode")
        return standard_logger
    elif server_name == "handwritten-mode":
        if handwritten_logger is None:
            handwritten_logger = ProcessingLogger("handwritten-mode")
        return handwritten_logger
    else:
        return ProcessingLogger(server_name)
