import { Bot, User } from "lucide-react"; // Import only necessary icons

const iconSize = 30;

export default function ChatBubble({ message, isUser }) {
  return (
    <div className={`flex ${isUser ? 'flex-row-reverse' : ''} py-2 gap-2`}>
      {/* User or AI Icon */}
      <div className={`flex items-start space-x-2 mb-2 ${isUser ? "mt-[-20px]" : "mr-[-17px]"} animate-fade-in-up`}>
        <div
          className={`rounded-full border-2 ${isUser ? "border-blue-300" : "border-white/30"} flex justify-center items-center p-1 ${!isUser ? "bg-white/10 backdrop-blur-sm" : ""}`}
          style={{ height: `${iconSize + 10}px`, width: `${iconSize + 10}px` }} // Adjust size for border
        >
          {isUser ? (
            <User className="text-black" style={{ height: `${iconSize}px`, width: `${iconSize}px` }} />
          ) : (
            <Bot className="text-white" style={{ height: `${iconSize}px`, width: `${iconSize}px` }} />
          )}
        </div>
      </div>

      {/* Chat bubble */}
      <div className={`flex flex-1 ${isUser ? "justify-end" : "justify-start"} animate-fade-in-up`}>
        <div
          className={`p-4 rounded-2xl text-sm relative group
            ${isUser ? 'max-w-[94%]' : 'max-w-[75%]'} whitespace-pre-wrap break-words
            ${isUser ? 'bg-gradient-to-r from-pink-600 to-blue-700 text-white rounded-br-none shadow-[0_4px_6px_rgba(0,0,0,0.1)]' : 'bg-white/10 text-white rounded-bl-none backdrop-blur-sm border border-white/20'}
            font-sans font-medium ${isUser ? 'rounded-tr-[0] rounded-br-xl' : 'rounded-tl-none'}`}
        >
          {/* Display plain text */}
          <p className="text-white">{message}</p>
        </div>
      </div>
    </div>
  );
}
