{"timestamp": "2025-07-30T15:01:01.198196", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:05.720673", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:07.000632", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:07.026949", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:07.028471", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:07.029167", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:07.029742", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:07.030246", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:28.180591", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:28.181876", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:39.131616", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:39.132766", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:39.133502", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:39.134229", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:39.134807", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:39.135621", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files are almost always processed in STANDARD_MODE, and the absence of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:40.953949", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250730_150140_75eac745_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:01:40.955318", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (12.77s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:05:08.912971", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:05:15.381671", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:05:16.655893", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:05:16.679874", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:05:16.680845", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:05:16.681736", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:05:16.682451", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:05:16.682989", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:05:57.741083", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:01.413922", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:02.397041", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:02.402336", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:02.403020", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:02.403482", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:02.403848", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:02.404133", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:06.828632", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:11.532230", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:12.458355", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:12.463491", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:12.464316", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:12.464994", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:12.465508", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:12.465977", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:21.197271", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:27.280898", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:30.501981", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:31.306803", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:31.312361", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:31.313157", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:31.314030", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:31.314996", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:31.315515", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:39.332639", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:39.333579", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:42.957860", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:42.958960", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:42.959660", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:42.960274", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:42.960892", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:42.961534", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is typically processed in STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given the file type and lack of indicators suggesting handwritten content, it is highly likely that this document is a printed or digital document, making STANDARD_MODE the most appropriate choice. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:47.919466", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250730_150647_c74d3e7a_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:06:47.921299", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (8.59s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:08.793200", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:13.867242", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:14.668154", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:14.671863", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:14.672150", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:14.672377", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:14.672620", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:14.672887", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:50.993116", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:50.994391", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:57.766351", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:57.767033", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:57.767405", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:57.767653", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:57.767833", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:07:57.768319", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files are almost always processed in STANDARD_MODE and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:01.783916", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250730_150801_83a65dad_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:01.784723", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (10.79s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:22.522624", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:22.656737", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:22.657070", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 2", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:22.657472", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:22.657799", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 9", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:46.887100", "level": "INFO", "logger": "main", "message": "Processing document: hand written invoice.png (43136 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:46.887748", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: hand written invoice.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:53.845932", "level": "INFO", "logger": "main", "message": "Document routing decision for hand written invoice.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:53.846916", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:53.847968", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:53.849101", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:53.850057", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:08:53.850717", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'hand written invoice.png' explicitly contains keywords 'hand', 'written', and 'invoice', indicating that the document is a handwritten invoice. This, combined with the file type being an image, suggests that the content is likely handwritten. Given the presence of handwriting keywords and the specific mention of 'invoice', it is reasonable to conclude that the document requires HANDWRITTEN_MODE for optimal processing.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:09:00.665914", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250730_150900_f808e601_hand written invoice.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:09:00.666604", "level": "INFO", "logger": "main", "message": "Document processed successfully: hand written invoice.png -> HANDWRITTEN_MODE (13.78s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:14:35.411114", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:14:40.504164", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:14:41.326595", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:14:41.349164", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:14:41.350272", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:14:41.350893", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:14:41.351448", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:14:41.351678", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:14:58.176285", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:01.858140", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:02.693322", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:02.697060", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:02.697335", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:02.697524", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:02.697803", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:02.698174", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:13.471142", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:17.103936", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:17.942745", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:17.946586", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:17.946853", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:17.947109", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:17.947300", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:15:17.947471", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:10.695162", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:14.135869", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:14.863116", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:14.880148", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:14.880416", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:14.880626", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:14.880846", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:14.881114", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:42.505166", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:45.817936", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:46.581845", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:46.586246", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:46.586755", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:46.587546", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:46.588371", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:29:46.588792", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:17.850781", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:21.268879", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:22.013233", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:22.018098", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:22.018580", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:22.019120", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:22.019447", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:22.019899", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:29.457096", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:33.180120", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:33.909487", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:33.913016", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:33.913304", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:33.913536", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:33.913751", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:33.913935", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:30:57.603426", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:31:01.259182", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:31:01.986742", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:31:01.990356", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:31:01.990587", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:31:01.990747", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:31:01.990899", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:31:01.991083", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:32:44.270581", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:32:47.999554", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:32:48.714839", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:32:48.719137", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:32:48.719481", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:32:48.719808", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:32:48.720614", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:32:48.720947", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:32:56.895367", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:00.320150", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:01.037306", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:01.041840", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:01.042233", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:01.042727", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:01.043274", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:01.043475", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:11.228009", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:14.808101", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:15.541444", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:15.546412", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:15.547269", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:15.547822", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:15.548633", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:33:15.548888", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:35:17.558842", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:35:21.204495", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:35:21.913125", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:35:21.917047", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:35:21.917819", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:35:21.918365", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:35:21.918760", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:35:21.919313", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:41:47.907894", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:41:53.299812", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:41:54.568800", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:41:54.597619", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:41:54.598469", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8080", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:41:54.599536", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:41:54.600089", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:41:54.600536", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:17.947948", "level": "INFO", "logger": "main", "message": "Processing document: data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:17.949584", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:22.463042", "level": "INFO", "logger": "main", "message": "Document routing decision for data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:22.465072", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:22.466194", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:22.466753", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:22.467522", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:22.468656", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is PDF, which is typically associated with digital documents, scanned printed text, or printed/typed invoices. Given the lack of any indicators suggesting handwritten content and the fact that PDF files are almost always processed in STANDARD_MODE, the decision to use STANDARD_MODE is made with high confidence. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:24.818022", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250730_154724_36edd707_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:47:24.819530", "level": "INFO", "logger": "main", "message": "Document processed successfully: data4.pdf -> STANDARD_MODE (6.87s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:50:04.300027", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:50:04.555990", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:50:04.557003", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:50:04.558107", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-30T15:50:04.558862", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:17:18.989131", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:17:24.585867", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:17:25.861568", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:17:25.872028", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:17:25.873001", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:17:25.874056", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:17:25.875089", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:17:25.875664", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:09.119373", "level": "INFO", "logger": "main", "message": "Processing document: data6.pdf (817115 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:09.121414", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data6.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:16.867261", "level": "INFO", "logger": "main", "message": "Document routing decision for data6.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:16.869790", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:16.871161", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:16.872630", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:16.873629", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:16.874823", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, suggesting it is a standard digital document. Given the file type and lack of indicators for handwritten content, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:21.092895", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_111921_c75330df_data6.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:21.095262", "level": "INFO", "logger": "main", "message": "Document processed successfully: data6.pdf -> STANDARD_MODE (11.98s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:57.502168", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:57.812939", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:57.813934", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:57.815087", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:19:57.816374", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:11.533293", "level": "INFO", "logger": "main", "message": "Processing document: data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:11.534806", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:15.490380", "level": "INFO", "logger": "main", "message": "Document routing decision for data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:15.491840", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:15.493445", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:15.494877", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:15.496148", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:15.497098", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, suggesting it is a standard digital document. Given the file type and lack of indicators for handwritten content, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:17.649733", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_112017_e6093cb0_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:20:17.651306", "level": "INFO", "logger": "main", "message": "Document processed successfully: data4.pdf -> STANDARD_MODE (6.12s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:23:49.927675", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:23:50.285154", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:23:50.286722", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:23:50.288019", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:23:50.289035", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:34:43.941187", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:38:21.314473", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:38:32.228261", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:38:35.092625", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:38:35.105521", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:38:35.106674", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:38:35.107548", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:38:35.108560", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T11:38:35.109566", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:17:42.536142", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:17:53.293370", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:17:55.746482", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:17:55.772796", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:17:55.773683", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:17:55.774329", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:17:55.774831", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:17:55.775291", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:25:35.574708", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:25:45.495847", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:25:48.561012", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:25:48.575125", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:25:48.576964", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:25:48.578455", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:25:48.579877", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:25:48.580638", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:20.121129", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:20.121945", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:25.237768", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:25.239198", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:25.240618", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:25.241781", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:25.243026", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:25.244180", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files are almost always processed in STANDARD_MODE, and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:26.886513", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_152826_1c14bf18_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:28:26.888545", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (6.77s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:27.301375", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:27.302155", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:31.035179", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:31.036311", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:31.037119", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:31.037948", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:31.040395", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:31.041113", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is typically processed in STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given the file type and lack of indicators for handwritten content, it is highly likely that this document is a printed or digital document, making STANDARD_MODE the most appropriate choice. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:32.683467", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_153732_844e1511_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:37:32.684463", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.38s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:43:58.315078", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:43:58.699012", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:43:58.700106", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 2", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:43:58.701123", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:43:58.702095", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 12", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:26.215128", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:26.216140", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:30.810424", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:30.811628", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:30.812721", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:30.813784", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:30.814898", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:30.816021", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files are almost always processed in STANDARD_MODE, and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:34.356017", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_154434_d458034d_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:34.357109", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (8.14s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:36.820956", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:36.822334", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:40.910477", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:40.911660", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:40.912683", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:40.913645", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:40.914704", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:40.915629", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is typically processed in STANDARD_MODE. The filename does not contain any keywords indicating handwritten content, and the file type is a strong indicator of printed or digital content. Given the lack of any specific indicators suggesting handwritten content and the general guideline that PDF files should almost always use STANDARD_MODE, the decision is made with high confidence. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:44.941629", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_154444_68e7b376_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:44.942563", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (8.12s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:53.886041", "level": "INFO", "logger": "main", "message": "Processing document: data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:53.887323", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:59.639625", "level": "INFO", "logger": "main", "message": "Document routing decision for data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:59.641165", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:59.642471", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:59.643652", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:59.644972", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:44:59.646003", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, suggesting it is a standard digital document. Given the file type and lack of indicators for handwritten content, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:06.027986", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_154505_8f2ff710_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:06.029257", "level": "INFO", "logger": "main", "message": "Document processed successfully: data4.pdf -> STANDARD_MODE (12.14s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:18.143152", "level": "INFO", "logger": "main", "message": "Processing document: data5.pdf (234064 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:18.143855", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data5.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:24.624369", "level": "INFO", "logger": "main", "message": "Document routing decision for data5.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:24.625420", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:24.626276", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:24.627130", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:24.627976", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:24.628844", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the detected keywords list is empty. Given that PDF files should almost always use STANDARD_MODE, and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:28.157567", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_154528_cf2b0bef_data5.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:45:28.158852", "level": "INFO", "logger": "main", "message": "Document processed successfully: data5.pdf -> STANDARD_MODE (10.02s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:48:37.368250", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:48:37.669784", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:48:37.670381", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 4", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:48:37.670996", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T15:48:37.671522", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 32", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:08.871784", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:08.873020", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:12.461508", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:12.462626", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:12.463549", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:12.464425", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:12.465610", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:12.466545", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename 'data2.pdf' does not contain any keywords suggesting handwritten content or specific invoice/receipt context. Given the file type and the lack of indicators for handwritten content, it is reasonable to conclude that this document is most likely a printed, typed, or digitally generated document, suitable for STANDARD_MODE processing. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:14.281170", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_160014_3b4529c5_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:00:14.282640", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.41s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:18.879589", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:19.203962", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:19.204859", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:19.205574", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:19.206209", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:46.608229", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:46.608805", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:53.356566", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:53.357531", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:53.358499", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:53.359318", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:53.360124", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:53.361000", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files should almost always use STANDARD_MODE, and there's no indication of handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:55.192925", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_160155_d8061dfa_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:01:55.194325", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (8.59s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:06:54.850058", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:06:55.208221", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:06:55.209342", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:06:55.210007", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:06:55.210784", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:08.145027", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:08.145570", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:11.889990", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:11.890980", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:11.891969", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:11.893020", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:11.893957", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:11.894877", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename 'data2.pdf' does not contain any keywords suggesting handwritten content or specific invoice/receipt context that might imply a need for HANDWRITTEN_MODE. Given that PDF files are almost always processed in STANDARD_MODE unless explicitly indicated as handwritten, and there's no such indication here, the decision leans heavily towards STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:13.476084", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_160713_e0d8ecb6_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:07:13.477446", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.33s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:12.699243", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:12.999306", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:12.999850", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:13.000248", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:13.000615", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:24.594861", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:24.597033", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:28.530649", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:28.531435", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:28.532323", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:28.532902", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:28.533485", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:28.534176", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename 'data2.pdf' does not contain any keywords suggesting handwritten content or specific document types like invoices/receipts. Given that PDF files are almost always processed in STANDARD_MODE and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is made with high confidence. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:30.273538", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_161130_44f8eb49_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:11:30.275547", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.68s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:17.656247", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:17.972321", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:17.972895", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:17.973373", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:17.973799", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:27.466434", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:27.467163", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:32.212287", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:32.213212", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:32.214142", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:32.214808", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:32.215508", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:32.216349", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files are almost always processed in STANDARD_MODE, and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:36.833487", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_162036_0d9bef66_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:20:36.838451", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (9.37s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:36.758567", "level": "INFO", "logger": "main", "message": "Processing document: data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:36.759866", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:41.124199", "level": "INFO", "logger": "main", "message": "Document routing decision for data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:41.125518", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:41.126391", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:41.126951", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:41.127476", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:41.127979", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files should almost always use STANDARD_MODE, and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:43.278591", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_162143_eaf7e6d8_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:43.279757", "level": "INFO", "logger": "main", "message": "Document processed successfully: data4.pdf -> STANDARD_MODE (6.52s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:52.252500", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:52.625512", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:52.626703", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 2", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:52.627904", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:21:52.628805", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 7", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:23:55.063114", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:23:55.065151", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:23:59.349407", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:23:59.350129", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:23:59.350503", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:23:59.350818", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:23:59.351116", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:23:59.351415", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename 'data2.pdf' does not contain any keywords suggesting handwritten content or specific invoice/receipt context that might imply a need for HANDWRITTEN_MODE. Given that PDF files are almost always processed in STANDARD_MODE unless explicitly indicated as handwritten, and there's no such indication here, the decision leans heavily towards STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:24:02.623259", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_162402_b7d97e42_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:24:02.624048", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (7.56s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:17.774385", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:17.776220", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:21.292793", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:21.294261", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:21.294659", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:21.294893", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:21.295381", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:21.296302", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename 'data2.pdf' does not contain any keywords suggesting handwritten content or specific document types like invoices/receipts. Given that PDF files are almost always processed in STANDARD_MODE, and there's no indication of handwritten content, the decision leans heavily towards STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:23.032839", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_163923_429027b8_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:39:23.034100", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.26s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:54.281600", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:54.283054", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:58.023705", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:58.024500", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:58.025799", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:58.026363", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:58.026852", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:58.027364", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files are almost always processed in STANDARD_MODE, and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:59.684003", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_164759_0318afe0_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:47:59.684713", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.40s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:25.200141", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:25.202691", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:29.700614", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:29.701893", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:29.702532", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:29.703089", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:29.704209", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:29.705141", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename 'data2.pdf' does not contain any keywords suggesting handwritten content or specific invoice/receipt context that might imply a need for HANDWRITTEN_MODE. Given that PDF files are almost always processed in STANDARD_MODE unless explicitly indicated as handwritten, and there's no such indication here, the decision leans heavily towards STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:32.789409", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_165832_fa5942d5_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T16:58:32.790836", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (7.59s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:23.228804", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:23.229685", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:27.127880", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:27.129000", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:27.129678", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:27.130327", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:27.131044", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:27.131601", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename 'data2.pdf' does not contain any keywords suggesting handwritten content or specific invoice/receipt context. Given the file type and the lack of indicators for handwritten content, it is reasonable to conclude that this document is most likely a printed, typed, or digitally generated document, making STANDARD_MODE the optimal choice for processing. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:30.517865", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_170330_7553b3ff_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:03:30.520000", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (7.29s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:23.735946", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:24.082453", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:24.083322", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 5", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:24.084151", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:24.085002", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 30", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:28.934343", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:29.277894", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:29.278898", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:29.279801", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:29.280706", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 30", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:38.793152", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:38.794098", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:43.891961", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:43.893145", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:43.893735", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:43.894454", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:43.895527", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:43.896501", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename 'data2.pdf' does not contain any keywords suggesting handwritten content or specific document types like invoices/receipts. Given that PDF files are almost always processed in STANDARD_MODE unless explicitly indicated as handwritten, and the lack of any handwriting keywords, the decision leans heavily towards STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:44.397378", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_170844_4c84c18f_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:08:44.403071", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.61s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:09:29.913130", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:09:30.248285", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:09:30.250793", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:09:30.255268", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:09:30.256695", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:12:57.442495", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:12:57.629808", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:12:57.630075", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:12:57.630298", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:12:57.630478", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:24.976619", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:24.977077", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:26.775026", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:26.775557", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:26.775722", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:26.775843", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:26.775985", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:26.776135", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:27.326089", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_171327_0fa1f36a_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:13:27.326709", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (2.35s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:42.404804", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:42.405288", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:44.555969", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:44.556458", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:44.556836", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:44.557227", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:44.557682", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:44.558040", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:45.018618", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250731_171445_0497485e_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-07-31T17:14:45.019433", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (2.61s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:19:38.865245", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:19:48.301116", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:19:51.345792", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:19:51.361629", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:19:51.362404", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:19:51.362965", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:19:51.363449", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:19:51.364344", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:24.821319", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:24.822418", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:27.734880", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:27.735739", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:27.736373", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:27.736955", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:27.737727", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:27.738439", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:28.431553", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_102028_e9294ede_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:20:28.432617", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (3.61s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:03.332599", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:03.575988", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:03.576744", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 3", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:03.577493", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:03.578240", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 3", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:38.243516", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:38.244550", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:41.569388", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:41.570959", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:41.571878", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:41.572844", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:41.573435", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:41.574118", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:42.100004", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_102142_a3fafb95_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:21:42.101745", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (3.86s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:36.198303", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:36.199286", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:38.067804", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:38.068835", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:38.069713", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:38.070487", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:38.071472", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:38.072401", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:38.554770", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_102238_213952c3_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:38.555839", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (2.36s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:39.028588", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:39.029364", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:41.424975", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:41.425985", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:41.426697", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:41.427299", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:41.427867", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:41.428349", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:41.763827", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_102241_eca88b82_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:41.766202", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (2.74s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:42.395699", "level": "INFO", "logger": "main", "message": "Processing document: data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:42.397671", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:43.894299", "level": "INFO", "logger": "main", "message": "Document routing decision for data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:43.895285", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:43.895933", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:43.897918", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:43.898946", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:43.900415", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:44.220236", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_102244_1e79305d_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:22:44.221550", "level": "INFO", "logger": "main", "message": "Document processed successfully: data4.pdf -> STANDARD_MODE (1.83s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:18.819554", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:18.820413", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:21.181253", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:21.182163", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:21.182764", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:21.183414", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:21.184085", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:21.184954", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:21.707000", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_102421_066b26fb_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:24:21.708538", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (2.89s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:02.177791", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:12.914480", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:16.106476", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:16.120366", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:16.121320", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:16.123540", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:16.124281", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:16.125016", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:50.413901", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:50.414633", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:54.918277", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:54.919417", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:54.922630", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:54.923660", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:54.924740", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:54.925555", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is pdf, which suggests a digital or printed document. Given that PDF files should almost always use STANDARD_MODE, and there are no indicators of handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:58.116307", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_102558_ce49493d_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:25:58.117256", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (7.70s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:47.010333", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:47.011307", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:50.434078", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:50.436659", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:50.437737", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:50.438836", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:50.439649", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:50.440385", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is pdf, which suggests a digital or printed document. Given that PDF files should almost always use STANDARD_MODE, and there are no indicators of handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:51.901817", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_102651_936f3506_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:26:51.903131", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (4.89s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:27.609598", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:27.610499", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:32.312899", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:32.313835", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:32.314434", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:32.315711", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:32.316660", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:32.317800", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files are almost always processed in STANDARD_MODE and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:33.833983", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_102733_f1d1943b_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:27:33.835563", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (6.23s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:41.810534", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:41.811144", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:45.581379", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:45.582241", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:45.582594", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:45.583029", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:45.583404", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:45.583559", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is PDF, which is typically associated with digital documents, scanned printed text, or computer-generated content. Given the lack of any indicators suggesting handwritten content and the fact that PDF files are almost always processed in STANDARD_MODE, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:50.121883", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_103050_f8a167e5_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:30:50.122766", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (8.31s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:51.645678", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:51.646207", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:54.739234", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:54.739605", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:54.739782", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:54.739916", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:54.740080", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:54.740209", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is pdf, which suggests a digital or printed document. Given that PDF files should almost always use STANDARD_MODE, and there are no indicators of handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:56.264298", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_103156_2e10942f_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:31:56.265136", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (4.62s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:03.450816", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:03.451432", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:09.816755", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:09.817711", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:09.818313", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:09.819314", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:09.819953", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:09.820408", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is PDF, which is typically associated with digital documents, scanned printed text, or printed/typed invoices. Given the lack of any indicators suggesting handwritten content and the fact that PDF files are almost always processed in STANDARD_MODE, the decision to use STANDARD_MODE is made with high confidence. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:11.427328", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_103511_fed2af10_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:35:11.428087", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (7.98s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:02.811375", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:02.812019", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:08.130170", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:08.130780", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:08.131163", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:08.131381", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:08.131950", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:08.132200", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is PDF, which is typically associated with digital documents, scanned printed text, or computer-generated content. Given the lack of any indicators suggesting handwritten content and the fact that PDF files are almost always processed in STANDARD_MODE, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:10.334965", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_103610_8d258ca2_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:36:10.335787", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (7.52s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:47.640872", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:47.641592", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:50.747003", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:50.747770", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:50.748552", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:50.749071", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:50.749858", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:50.750204", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, suggesting it is a standard digital document. Given the file type and lack of indicators for handwritten content, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:53.639671", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_103753_edcc3c12_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:37:53.640183", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (6.00s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:41:56.045959", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:41:56.046700", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:41:59.618293", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:41:59.620896", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:41:59.622214", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:41:59.623892", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:41:59.624601", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:41:59.625376", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename 'data2.pdf' does not contain any keywords suggesting handwritten content or specific invoice/receipt context. Given that PDF files are almost always processed in STANDARD_MODE, and the absence of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is made with high confidence. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:42:02.638428", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_104202_81e94048_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:42:02.639408", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (6.59s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:15.451526", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:15.452842", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:19.635657", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:19.637531", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:19.638407", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:19.639222", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:19.639806", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:19.640352", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is pdf, which suggests a digital or printed document. Given that PDF files should almost always use STANDARD_MODE, and there are no indicators of handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:21.317720", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_105021_a5a4c575_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:50:21.319312", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.87s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:51:17.500198", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:51:18.064163", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:51:18.065040", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 15", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:51:18.065796", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:51:18.066736", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 85", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:53:54.985503", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:53:54.987277", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:53:58.349761", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:53:58.351258", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:53:58.352353", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:53:58.354089", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:53:58.355665", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:53:58.356418", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename 'invoices/data2.pdf' suggests it is an invoice, and the presence of the keyword 'invoice' further supports this. There are no indications of handwritten content, and business documents like invoices are usually printed unless specifically noted as handwritten. Therefore, STANDARD_MODE is the most suitable processing mode for this document. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:01.778754", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_105401_b9d3c4a0_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:01.779609", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data2.pdf -> STANDARD_MODE (6.79s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:04.474818", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:04.475656", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:07.635090", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:07.636526", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:07.637305", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:07.638301", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:07.639238", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:07.640089", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename 'invoices/data3.pdf' suggests a business document, and the detected keyword 'invoice' implies a printed or typed invoice. The absence of handwriting keywords in the filename further supports this conclusion. Given the file type and content prediction, STANDARD_MODE is the most suitable processing mode for this document. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:11.256540", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_105411_86376ccd_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:11.258081", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data3.pdf -> STANDARD_MODE (6.78s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:19.333789", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:19.335349", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:20.685016", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:20.685924", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:20.686576", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:20.687649", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:20.688801", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:20.689770", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:21.132327", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_105421_10402d51_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:21.134360", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data4.pdf -> STANDARD_MODE (1.80s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:29.545916", "level": "INFO", "logger": "main", "message": "Processing document: invoices/dataarab1.pdf (7041 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:29.546701", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/dataarab1.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:31.118118", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/dataarab1.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:31.119355", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:31.120065", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:31.120862", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:31.121628", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:31.122192", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:31.667493", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_105431_820b2d85_dataarab1.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:54:31.668808", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/dataarab1.pdf -> STANDARD_MODE (2.12s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:27.678777", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:27.679397", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:29.378002", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:29.379640", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:29.380651", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:29.381658", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:29.382703", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:29.383312", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:29.767395", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_105629_0cd7096a_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T10:56:29.768509", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (2.09s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:03:59.577451", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:04:10.061774", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:04:13.317496", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:04:13.332720", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:04:13.333875", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:04:13.335189", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:04:13.336000", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:04:13.336816", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:05:24.025091", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:05:29.009184", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:05:31.895484", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:05:31.908165", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:05:31.909067", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:05:31.909887", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:05:31.910621", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:05:31.911328", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:06:28.068082", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:06:28.270049", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:06:28.270312", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 5", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:06:28.270494", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:06:28.270661", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 25", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:08.661237", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:17.958552", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:20.708496", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:20.721116", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:20.722272", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:20.723336", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:20.724349", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:20.725291", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:58.263148", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:58.421865", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:58.422404", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:58.422821", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:08:58.423105", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:10:00.346174", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:10:09.750367", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:10:12.597395", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:10:12.611666", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:10:12.612171", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:10:12.612482", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:10:12.613537", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:10:12.614464", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:34:57.935836", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:34:57.936350", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:35:02.539871", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:35:02.540445", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:35:02.540819", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:35:02.541121", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:35:02.543340", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:35:02.543792", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is pdf, which suggests a digital or printed document. Given that PDF files should almost always use STANDARD_MODE, and there are no indicators of handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:35:04.031251", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_113504_42290578_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:35:04.032053", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (6.10s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:19.524800", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:19.526495", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:23.283818", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:23.284990", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:23.285908", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:23.286838", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:23.287844", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:23.290842", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is pdf, which suggests a digital or scanned printed document. Given that PDF files should almost always use STANDARD_MODE, and there are no indicators of handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:28.167679", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_113928_e8e88430_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:39:28.168626", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (8.64s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:21.992489", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:21.993808", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:25.513226", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:25.514284", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:25.515149", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:25.516170", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:25.517043", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:25.517954", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is pdf, which suggests a digital or printed document. Given that PDF files should almost always use STANDARD_MODE, and there are no indicators of handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:27.422275", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_114127_709c95cf_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:41:27.423610", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.43s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:46:54.393200", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:46:54.695969", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:46:54.696793", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 3", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:46:54.697385", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:46:54.697923", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 13", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:08.122843", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:08.399794", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:08.400615", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:08.401478", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:08.402200", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 13", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:27.322436", "level": "INFO", "logger": "main", "message": "Processing document: data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:27.323398", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:31.484245", "level": "INFO", "logger": "main", "message": "Document routing decision for data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:31.486255", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:31.487599", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:31.488677", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:31.489680", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:31.490696", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is PDF, which is typically associated with digital documents, scanned printed text, or computer-generated content. Given the lack of any indicators suggesting handwritten content and the fact that PDF files are almost always processed in STANDARD_MODE, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:33.385988", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_114733_8864cb20_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:47:33.387350", "level": "INFO", "logger": "main", "message": "Document processed successfully: data4.pdf -> STANDARD_MODE (6.06s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:50:58.673171", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:50:58.926833", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:50:58.927747", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:50:58.928990", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:50:58.929856", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 3", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:51:22.289002", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:51:22.554496", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:51:22.555248", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:51:22.556021", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:51:22.556711", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 3", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:00.951598", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:01.249809", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:01.251819", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:01.253258", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:01.254611", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 3", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:35.611468", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:35.926135", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:35.926632", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:35.927109", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:52:35.927484", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 3", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:03.062608", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:03.063635", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:07.826131", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:07.829256", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:07.830268", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:07.831922", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:07.833278", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:07.834384", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename 'invoices/data2.pdf' suggests a business document, likely an invoice, and does not contain any keywords indicating handwritten content. The detected keyword 'invoice' further supports the assumption that this is a printed or digital document. Given the file type and the lack of indicators for handwritten content, STANDARD_MODE is the most appropriate processing mode. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:12.129666", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_115412_f2362389_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:12.131180", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data2.pdf -> STANDARD_MODE (9.07s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:14.696702", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:14.697614", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:19.252863", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:19.253996", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:19.255077", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:19.256314", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:19.257403", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:19.258519", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename 'invoices/data3.pdf' suggests a business document, and the detected keyword 'invoice' further supports this. Since PDF files are usually processed in STANDARD_MODE and there are no indications of handwritten content, this mode is the most suitable for optimal processing. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:22.628045", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_115422_87dac93d_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:22.629423", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data3.pdf -> STANDARD_MODE (7.93s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:27.795561", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:27.796632", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:31.756735", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:31.757716", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:31.758590", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:31.762349", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:31.763215", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:31.764115", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename 'invoices/data4.pdf' suggests a business document, likely an invoice, and does not contain any keywords indicating handwritten content. Given the file type and the absence of handwriting keywords, it is reasonable to conclude that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:33.987860", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_115433_a99d4db8_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:33.989010", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data4.pdf -> STANDARD_MODE (6.19s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:41.409034", "level": "INFO", "logger": "main", "message": "Processing document: invoices/dataarab1.pdf (7041 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:41.410192", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/dataarab1.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:52.717121", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/dataarab1.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:52.718235", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:52.719098", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:52.720098", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:52.721355", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:54:52.722362", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename does not contain any handwriting keywords, but it does contain the keyword 'invoice', suggesting a business document. Given that PDF files are almost always processed in STANDARD_MODE and the absence of any indicators suggesting handwritten content, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:55:07.348634", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_115507_ac26bc6b_dataarab1.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T11:55:07.349896", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/dataarab1.pdf -> STANDARD_MODE (25.94s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:19:01.840724", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:19:02.091928", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:19:02.092380", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 4", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:19:02.092837", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:19:02.093434", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 22", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:32:36.770370", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:32:37.143558", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:32:37.145574", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:32:37.146959", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:32:37.148083", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 22", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:32:59.052435", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:32:59.054004", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:02.771420", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:02.772587", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:02.773723", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:02.774649", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:02.775635", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:02.777728", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is pdf, which suggests a digital or printed document. Given that PDF files should almost always use STANDARD_MODE, and there are no indicators of handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:04.576833", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_123304_1ee06e6b_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:04.579672", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.53s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:16.832047", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:17.064041", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:17.065125", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:17.066132", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T12:33:17.067059", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:50:56.726042", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:50:56.727534", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:03.297855", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:03.298963", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:03.299974", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:03.300919", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:03.302166", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:03.303260", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:07.392785", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_135107_f206861a_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:07.394277", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (10.67s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:12.482066", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:12.774626", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:12.775904", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:12.776903", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T13:51:12.778074", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:08.770879", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:08.772751", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:12.957405", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:12.958998", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:12.959905", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:12.960644", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:12.961344", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:12.961911", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the detected keywords list is empty. Given that PDF files are almost always processed in STANDARD_MODE and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:14.807308", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_142414_5d060e7f_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:24:14.808825", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (6.04s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:44.334934", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:44.336200", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:50.670372", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:50.671635", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:50.672644", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:50.673599", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:50.674549", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:50.675248", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is PDF, which is typically associated with digital documents, scanned printed text, or computer-generated content. Given the file type and the lack of any indicators suggesting handwritten content, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:52.288703", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_142552_116a9197_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:25:52.290094", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (7.96s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:26:41.068693", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:26:41.364708", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:26:41.365785", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 2", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:26:41.366769", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T14:26:41.367553", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 13", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:04:19.926088", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:04:19.927234", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:06:48.920805", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:06:48.922867", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:06:48.923684", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:06:48.924796", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:06:48.925740", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:06:48.926721", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'handwritten.png' explicitly contains keywords indicating handwritten content, such as 'handwritten', 'hand', and 'written'. Given that it's an image file, the presence of these keywords strongly suggests that the document is handwritten. Since the file is not a PDF and the filename analysis points towards handwritten content, HANDWRITTEN_MODE is the most appropriate processing mode.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:03.581046", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_150703_45c834fe_handwritten.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:03.581963", "level": "INFO", "logger": "main", "message": "Document processed successfully: handwritten.png -> HANDWRITTEN_MODE (163.66s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:04.441515", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:04.442177", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:56.871226", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:56.875732", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:56.876381", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:56.877582", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:56.880159", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:07:56.885306", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'handwritten.png' explicitly contains keywords indicating handwritten content, such as 'handwritten', 'hand', and 'written'. Given that it's an image file and not a PDF, and the presence of these keywords, it's highly likely that the document contains handwritten text. This aligns with the criteria for HANDWRITTEN_MODE, which includes handwritten invoices, hand-filled forms, and handwritten notes.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:08:47.675483", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:08:57.479931", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:00.397357", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:00.412161", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:00.412963", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:00.413723", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:00.414448", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:00.415343", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:46.408207", "level": "INFO", "logger": "main", "message": "Processing document: hand written invoice.png (43136 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:46.410998", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: hand written invoice.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:57.893631", "level": "INFO", "logger": "main", "message": "Document routing decision for hand written invoice.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:57.895297", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:57.896213", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:57.897032", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:57.897791", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:09:57.898817", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'hand written invoice.png' explicitly contains keywords 'hand', 'written', and 'invoice', indicating that the document is a handwritten invoice. This, combined with the file type being an image, suggests that the content is likely handwritten. Given the presence of handwriting keywords and the specific mention of 'invoice', it is reasonable to conclude that the document is a handwritten invoice, which falls under the HANDWRITTEN_MODE category.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:10:07.213828", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_151007_1e8ea6cf_hand written invoice.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:10:07.214962", "level": "INFO", "logger": "main", "message": "Document processed successfully: hand written invoice.png -> HANDWRITTEN_MODE (20.81s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:12.797484", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:13.068952", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:13.069868", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 2", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:13.070842", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:13.072891", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 8", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:24.314941", "level": "INFO", "logger": "main", "message": "Processing document: hand written invoice.png (43136 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:24.315332", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: hand written invoice.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:26.790666", "level": "INFO", "logger": "main", "message": "Document routing decision for hand written invoice.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:26.792149", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:26.793142", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:26.793888", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:26.794710", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:26.795415", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:27.004224", "level": "INFO", "logger": "main", "message": "Primary processing failed, trying fallback mode", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:45.322828", "level": "INFO", "logger": "main", "message": "Fallback processing successful with HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:45.327811", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_151645_1311e535_hand written invoice.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:16:45.329035", "level": "INFO", "logger": "main", "message": "Document processed successfully: hand written invoice.png -> HANDWRITTEN_MODE (21.01s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:29.456635", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:29.458356", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:37.408421", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:37.409392", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:37.410081", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:37.410542", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:37.410922", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:37.411371", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:38.064157", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_151738_80f6edc9_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:17:38.065015", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (8.61s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:11.155216", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:11.156383", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:14.278113", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:14.279110", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:14.279984", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:14.280828", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:14.281719", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:14.282795", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and the file type is PDF, which is typically associated with digital documents, scanned printed text, or printed/typed invoices. Given the lack of any indicators suggesting handwritten content and the fact that PDF files are almost always processed in STANDARD_MODE, the decision to use STANDARD_MODE is made with high confidence. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:15.299330", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_152715_10df5681_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:27:15.300527", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (4.15s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:04.317341", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:17.222548", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:20.377252", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:20.398119", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:20.400606", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:20.401672", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:20.402607", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:20.403363", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:24.626673", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:24.627617", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:29.929618", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:29.930830", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:29.931791", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:29.933069", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:29.933922", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:29.934993", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is typically processed in STANDARD_MODE. The filename does not contain any keywords indicating handwritten content or invoice/receipt context. Given the file type and lack of specific indicators for handwritten content, it is reasonable to default to STANDARD_MODE with high confidence. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:33.654238", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250801_152833_4ebc2dc0_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:28:33.655512", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (9.03s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:29:33.661127", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-01T15:29:33.662655", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:19:48.330369", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:19:59.998580", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.213576", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.228965", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.229991", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.230818", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.231734", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.232593", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:22.826851", "level": "INFO", "logger": "main", "message": "Processing document: hand written invoice.png (43136 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:22.827559", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: hand written invoice.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.551179", "level": "INFO", "logger": "main", "message": "Document routing decision for hand written invoice.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.551992", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.552387", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.553050", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.553805", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.554231", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'hand written invoice.png' explicitly contains keywords 'hand', 'written', and 'invoice', indicating that the document is a handwritten invoice. This, combined with the file type being an image, suggests that the content is likely handwritten. Given the presence of handwriting keywords and the specific mention of 'invoice', it is reasonable to conclude that the document requires HANDWRITTEN_MODE for optimal processing.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:36.488593", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_102036_bde958e6_hand written invoice.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:36.490082", "level": "INFO", "logger": "main", "message": "Document processed successfully: hand written invoice.png -> HANDWRITTEN_MODE (13.66s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:46.975425", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:47.332473", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:47.333788", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 5", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:47.334624", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:47.335589", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 14", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:23:51.980234", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:23:51.981359", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.481931", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.484191", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.485640", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.486330", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.487192", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.487821", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files are almost always processed in STANDARD_MODE, and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:07.086989", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_102407_c163caee_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:07.088247", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (15.11s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:10.025470", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:10.026289", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.653577", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.654620", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.655698", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.656447", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.658703", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.661640", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.900724", "level": "INFO", "logger": "main", "message": "Primary processing failed, trying fallback mode", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:28:17.906213", "level": "INFO", "logger": "main", "message": "Fallback processing successful with HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:28:17.908013", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_102817_da55869c_handwritten.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:28:17.908365", "level": "INFO", "logger": "main", "message": "Document processed successfully: handwritten.png -> HANDWRITTEN_MODE (127.88s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:48.833170", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:48.833847", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.675781", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.676269", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.676676", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.676888", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.677072", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.677256", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files should almost always use STANDARD_MODE and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:54.262995", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_103854_bc9f4df8_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:54.263914", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.43s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:19.691332", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:19.691869", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.717588", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.718144", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.718740", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.719694", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.720409", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.720997", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'handwritten.png' explicitly contains keywords indicating handwritten content, such as 'handwritten', 'hand', and 'written'. Given that it's an image file and not a PDF, and the presence of these keywords, it's highly likely that the document contains handwritten text. This aligns with the criteria for HANDWRITTEN_MODE, which includes handwritten invoices, hand-filled forms, and handwritten notes.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:40:08.045446", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_104008_86ee03e8_handwritten.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:40:08.045921", "level": "INFO", "logger": "main", "message": "Document processed successfully: handwritten.png -> HANDWRITTEN_MODE (48.35s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:41:53.372268", "level": "INFO", "logger": "main", "message": "Processing document: hand written invoice.png (43136 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:41:53.372879", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: hand written invoice.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.482141", "level": "INFO", "logger": "main", "message": "Document routing decision for hand written invoice.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.482768", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.483222", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.483590", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.483863", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.484140", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'hand written invoice.png' explicitly contains keywords 'hand', 'written', and 'invoice', indicating that the document is a handwritten invoice. This, combined with the file type being an image, suggests that the content is likely to be handwritten. Given the presence of handwriting keywords and the specific mention of 'invoice', it is reasonable to conclude that the document requires HANDWRITTEN_MODE for optimal processing.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:08.477640", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_104208_5edab5e7_hand written invoice.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:08.478136", "level": "INFO", "logger": "main", "message": "Document processed successfully: hand written invoice.png -> HANDWRITTEN_MODE (15.11s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.144854", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.385789", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.386079", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 5", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.386248", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.386405", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 23", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:55.421237", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:06.021011", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.570803", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.579668", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.580068", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.580446", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.580920", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.581422", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:18.287670", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:25.950490", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.348237", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.390103", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.390719", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.391365", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.391922", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.392361", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:40.186194", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:59.563669", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.263515", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.309647", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.310677", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.311321", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.311866", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.312405", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:14.526109", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:23.484374", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.391622", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.415150", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.416370", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.417600", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.418334", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.419184", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:35.567911", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:44.575376", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.401999", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.426033", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.427249", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.428230", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.429185", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.430040", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:10.391573", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:18.111505", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.907117", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.925491", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.926048", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.926624", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.927027", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.927405", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:21.030099", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:28.308207", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.121753", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.144753", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.145798", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.146635", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.147426", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.147945", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:35.856972", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:43.890857", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.772595", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.793252", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.794379", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.795406", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.796186", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.797067", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:58.887860", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:06.075407", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.644621", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.655448", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.655669", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.655819", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.655924", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.656022", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:21.050985", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:30.102938", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.569089", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.587614", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.588153", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.588489", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.589089", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.589520", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:38.242486", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:48.359659", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:50.982795", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.001601", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.002177", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.002624", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.002984", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.003372", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:55.368381", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:04.894435", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.560638", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.570142", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.570873", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.571427", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.571942", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.572604", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:08.782527", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:15.739177", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.526088", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.548686", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.549462", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.550125", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.550969", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.551614", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:43:56.405947", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:05.893083", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:08.987660", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.001843", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.003027", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.004235", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.005173", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.006104", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:17.463071", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:17.464078", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.570544", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.572070", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.573183", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.574264", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.575274", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.576596", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'handwritten.png' explicitly contains keywords indicating handwritten content, such as 'handwritten', 'hand', and 'written'. Given that it's an image file and not a PDF, and the presence of these keywords, it's highly likely that the document contains handwritten text. Therefore, HANDWRITTEN_MODE is the most appropriate processing mode for this document.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:47:08.510913", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_114708_74e900d8_handwritten.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:47:08.512404", "level": "INFO", "logger": "main", "message": "Document processed successfully: handwritten.png -> HANDWRITTEN_MODE (51.05s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:39.919135", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:39.920393", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.713058", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.714738", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.715170", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.715649", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.716017", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.716301", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'handwritten.png' explicitly contains keywords indicating handwritten content, such as 'handwritten', 'hand', and 'written'. Given that it's an image file and not a PDF, and the presence of these keywords, it's highly likely that the document contains handwritten text. This aligns with the criteria for HANDWRITTEN_MODE, which includes handwritten invoices, hand-filled forms, and handwritten notes.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:56:29.641853", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_115629_140472e6_handwritten.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:56:29.642869", "level": "INFO", "logger": "main", "message": "Document processed successfully: handwritten.png -> HANDWRITTEN_MODE (49.72s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:04.931136", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:04.932142", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.478525", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.479894", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.481040", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.482070", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.483065", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.485290", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename contains the keyword 'invoice', suggesting it is a business document, and business documents are usually printed unless specifically noted as handwritten. There are no handwriting keywords in the filename, further supporting the choice of STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:12.136776", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_120012_9cdb12f6_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:12.137971", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data2.pdf -> STANDARD_MODE (7.21s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:17.068608", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:17.069293", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.208246", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.209356", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.210384", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.211326", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.212258", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.213145", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename 'invoices/data3.pdf' suggests it is an invoice, and the absence of handwriting keywords further supports this conclusion. Given the file type and content prediction, it is highly likely that this document is a printed or digital invoice, making STANDARD_MODE the optimal processing mode. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:25.733397", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_120025_054a386a_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:25.735553", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data3.pdf -> STANDARD_MODE (8.67s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:35.537685", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:35.538517", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.703790", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.704952", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.705963", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.706821", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.707889", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.708944", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename 'invoices/data4.pdf' suggests a business document, likely an invoice, and does not contain any keywords indicating handwritten content. The detected keyword 'invoice' further supports the assumption that this is a printed or digital document. Given the file type and filename analysis, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:55.633028", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_120055_febc5092_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:55.634439", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data4.pdf -> STANDARD_MODE (20.10s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:07.233679", "level": "INFO", "logger": "main", "message": "Processing document: invoices/dataarab1.pdf (7041 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:07.236035", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/dataarab1.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.391526", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/dataarab1.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.392795", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.393857", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.395218", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.396421", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.397978", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename does not contain any handwriting keywords, but it does contain the keyword 'invoice', suggesting a business document. Given that PDF files are almost always processed in STANDARD_MODE and business documents are usually printed, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:14.828642", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_120114_74afb029_dataarab1.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:14.830078", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/dataarab1.pdf -> STANDARD_MODE (7.60s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.633142", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.956291", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.957278", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.958316", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.959293", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 36", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:09.117612", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:09.118797", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.891256", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.892669", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.893655", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.894811", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.897333", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.898942", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files should almost always use STANDARD_MODE, and the absence of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:18.654234", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_121818_71d87666_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:18.655540", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (9.54s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.327911", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.627154", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.627710", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.628105", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.628584", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:41.677657", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:41.678827", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.947031", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.948312", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.949486", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.950565", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.951661", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.952702", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:52.038250", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_140552_295a3f82_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:52.039572", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (10.36s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.118385", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.426122", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.427929", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.429066", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.430680", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:51.524545", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:51.525489", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.657785", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.659808", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.661146", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.662028", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.662988", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.664434", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:11:01.724350", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_141101_3bb38f33_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:11:01.727064", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (10.20s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:22.826310", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:23.184682", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:23.185132", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:23.185995", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:23.186173", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:13.422007", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:13.423258", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.167469", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.169478", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.170675", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.171726", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.173193", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.174158", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is typically processed in STANDARD_MODE. The filename does not contain any keywords indicating handwritten content or invoice/receipt context. Given the file type and lack of specific indicators, it is highly likely that this document is a printed or digital document, suitable for STANDARD_MODE processing. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:20.128094", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_141320_fe2c43ee_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:20.129396", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (6.71s)", "module": "unified_logging", "function": "info", "line": 244}
