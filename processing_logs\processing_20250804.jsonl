{"timestamp": "2025-08-04T10:19:48.330369", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:19:59.998580", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.213576", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.228965", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.229991", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.230818", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.231734", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:03.232593", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:22.826851", "level": "INFO", "logger": "main", "message": "Processing document: hand written invoice.png (43136 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:22.827559", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: hand written invoice.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:22.828248", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: hand written invoice.png", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T10:20:24.288677", "level": "INFO", "logger": "agents", "message": "Document analysis for hand written invoice.png: HANDWRITTEN_MODE (confidence: 0.9)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T10:20:24.289598", "level": "INFO", "logger": "agents", "message": "Routing hand written invoice.png to HANDWRITTEN_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T10:20:24.290038", "level": "INFO", "logger": "agents", "message": "Processing hand written invoice.png in HANDWRITTEN_MODE", "module": "agents", "function": "_process_handwritten_mode", "line": 451}
{"timestamp": "2025-08-04T10:20:31.551179", "level": "INFO", "logger": "main", "message": "Document routing decision for hand written invoice.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.551992", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.552387", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.553050", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.553805", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:31.554231", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'hand written invoice.png' explicitly contains keywords 'hand', 'written', and 'invoice', indicating that the document is a handwritten invoice. This, combined with the file type being an image, suggests that the content is likely handwritten. Given the presence of handwriting keywords and the specific mention of 'invoice', it is reasonable to conclude that the document requires HANDWRITTEN_MODE for optimal processing.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:36.488593", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_102036_bde958e6_hand written invoice.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:20:36.490082", "level": "INFO", "logger": "main", "message": "Document processed successfully: hand written invoice.png -> HANDWRITTEN_MODE (13.66s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:46.975425", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:47.332473", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:47.333788", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 5", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:47.334624", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:21:47.335589", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 14", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:23:51.980234", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:23:51.981359", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:23:51.982260", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: data2.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T10:23:57.958918", "level": "INFO", "logger": "agents", "message": "Document analysis for data2.pdf: STANDARD_MODE (confidence: 0.95)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T10:23:57.959754", "level": "INFO", "logger": "agents", "message": "Routing data2.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T10:23:57.960233", "level": "INFO", "logger": "agents", "message": "Processing data2.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T10:24:05.481931", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.484191", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.485640", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.486330", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.487192", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:05.487821", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files are almost always processed in STANDARD_MODE, and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:07.086989", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_102407_c163caee_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:24:07.088247", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (15.11s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:10.025470", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:10.026289", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:10.027423", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: handwritten.png", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T10:26:11.430926", "level": "ERROR", "logger": "agents", "message": "Error analyzing document handwritten.png: Connection error.", "module": "agents", "function": "analyze_document", "line": 190}
{"timestamp": "2025-08-04T10:26:11.437689", "level": "INFO", "logger": "agents", "message": "Routing handwritten.png to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T10:26:11.438372", "level": "INFO", "logger": "agents", "message": "Processing handwritten.png in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T10:26:12.653577", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.654620", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.655698", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.656447", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.658703", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.661640", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:26:12.900724", "level": "INFO", "logger": "main", "message": "Primary processing failed, trying fallback mode", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:28:17.906213", "level": "INFO", "logger": "main", "message": "Fallback processing successful with HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:28:17.908013", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_102817_da55869c_handwritten.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:28:17.908365", "level": "INFO", "logger": "main", "message": "Document processed successfully: handwritten.png -> HANDWRITTEN_MODE (127.88s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:48.833170", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:48.833847", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:48.834091", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: data2.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T10:38:49.790376", "level": "INFO", "logger": "agents", "message": "Document analysis for data2.pdf: STANDARD_MODE (confidence: 0.95)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T10:38:49.791112", "level": "INFO", "logger": "agents", "message": "Routing data2.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T10:38:49.791417", "level": "INFO", "logger": "agents", "message": "Processing data2.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T10:38:52.675781", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.676269", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.676676", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.676888", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.677072", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:52.677256", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files should almost always use STANDARD_MODE and the lack of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:54.262995", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_103854_bc9f4df8_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:38:54.263914", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (5.43s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:19.691332", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:19.691869", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:19.692469", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: handwritten.png", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T10:39:20.555220", "level": "INFO", "logger": "agents", "message": "Document analysis for handwritten.png: HANDWRITTEN_MODE (confidence: 0.9)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T10:39:20.556076", "level": "INFO", "logger": "agents", "message": "Routing handwritten.png to HANDWRITTEN_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T10:39:20.556622", "level": "INFO", "logger": "agents", "message": "Processing handwritten.png in HANDWRITTEN_MODE", "module": "agents", "function": "_process_handwritten_mode", "line": 451}
{"timestamp": "2025-08-04T10:39:26.717588", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.718144", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.718740", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.719694", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.720409", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:39:26.720997", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'handwritten.png' explicitly contains keywords indicating handwritten content, such as 'handwritten', 'hand', and 'written'. Given that it's an image file and not a PDF, and the presence of these keywords, it's highly likely that the document contains handwritten text. This aligns with the criteria for HANDWRITTEN_MODE, which includes handwritten invoices, hand-filled forms, and handwritten notes.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:40:08.045446", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_104008_86ee03e8_handwritten.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:40:08.045921", "level": "INFO", "logger": "main", "message": "Document processed successfully: handwritten.png -> HANDWRITTEN_MODE (48.35s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:41:53.372268", "level": "INFO", "logger": "main", "message": "Processing document: hand written invoice.png (43136 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:41:53.372879", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: hand written invoice.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:41:53.373294", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: hand written invoice.png", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T10:41:54.700352", "level": "INFO", "logger": "agents", "message": "Document analysis for hand written invoice.png: HANDWRITTEN_MODE (confidence: 0.9)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T10:41:54.701056", "level": "INFO", "logger": "agents", "message": "Routing hand written invoice.png to HANDWRITTEN_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T10:41:54.701496", "level": "INFO", "logger": "agents", "message": "Processing hand written invoice.png in HANDWRITTEN_MODE", "module": "agents", "function": "_process_handwritten_mode", "line": 451}
{"timestamp": "2025-08-04T10:42:02.482141", "level": "INFO", "logger": "main", "message": "Document routing decision for hand written invoice.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.482768", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.483222", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.483590", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.483863", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:02.484140", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'hand written invoice.png' explicitly contains keywords 'hand', 'written', and 'invoice', indicating that the document is a handwritten invoice. This, combined with the file type being an image, suggests that the content is likely to be handwritten. Given the presence of handwriting keywords and the specific mention of 'invoice', it is reasonable to conclude that the document requires HANDWRITTEN_MODE for optimal processing.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:08.477640", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_104208_5edab5e7_hand written invoice.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:42:08.478136", "level": "INFO", "logger": "main", "message": "Document processed successfully: hand written invoice.png -> HANDWRITTEN_MODE (15.11s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.144854", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.385789", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.386079", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 5", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.386248", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:40.386405", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 23", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:47:55.421237", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:06.021011", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.570803", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.579668", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.580068", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.580446", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.580920", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:08.581422", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:18.287670", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:25.950490", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.348237", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.390103", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.390719", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.391365", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.391922", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:28.392361", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:40.186194", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:48:59.563669", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.263515", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.309647", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.310677", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.311321", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.311866", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:02.312405", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:14.526109", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:23.484374", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.391622", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.415150", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.416370", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.417600", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.418334", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:26.419184", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:35.567911", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:44.575376", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.401999", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.426033", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.427249", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.428230", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.429185", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:49:47.430040", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:10.391573", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:18.111505", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.907117", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.925491", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.926048", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.926624", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.927027", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:20.927405", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:21.030099", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:28.308207", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.121753", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.144753", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.145798", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.146635", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.147426", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:31.147945", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:35.856972", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:43.890857", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.772595", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.793252", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.794379", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.795406", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.796186", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:46.797067", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:50:58.887860", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:06.075407", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.644621", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.655448", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.655669", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.655819", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.655924", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:08.656022", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:21.050985", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:30.102938", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.569089", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.587614", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.588153", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.588489", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.589089", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:32.589520", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:38.242486", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:48.359659", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:50.982795", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.001601", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.002177", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.002624", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.002984", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:51.003372", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:51:55.368381", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:04.894435", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.560638", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.570142", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.570873", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.571427", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.571942", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:52:07.572604", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:08.782527", "level": "INFO", "logger": "main", "message": "Application shutdown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:15.739177", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.526088", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.548686", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.549462", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.550125", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.550969", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T10:53:18.551614", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:43:56.405947", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:05.893083", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:08.987660", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.001843", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.003027", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.004235", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.005173", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:44:09.006104", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:17.463071", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:17.464078", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:17.465064", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: handwritten.png", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T11:46:18.623776", "level": "INFO", "logger": "agents", "message": "Document analysis for handwritten.png: HANDWRITTEN_MODE (confidence: 0.9)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T11:46:18.624907", "level": "INFO", "logger": "agents", "message": "Routing handwritten.png to HANDWRITTEN_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T11:46:18.625489", "level": "INFO", "logger": "agents", "message": "Processing handwritten.png in HANDWRITTEN_MODE", "module": "agents", "function": "_process_handwritten_mode", "line": 451}
{"timestamp": "2025-08-04T11:46:24.570544", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.572070", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.573183", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.574264", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.575274", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:46:24.576596", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'handwritten.png' explicitly contains keywords indicating handwritten content, such as 'handwritten', 'hand', and 'written'. Given that it's an image file and not a PDF, and the presence of these keywords, it's highly likely that the document contains handwritten text. Therefore, HANDWRITTEN_MODE is the most appropriate processing mode for this document.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:47:08.510913", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_114708_74e900d8_handwritten.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:47:08.512404", "level": "INFO", "logger": "main", "message": "Document processed successfully: handwritten.png -> HANDWRITTEN_MODE (51.05s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:39.919135", "level": "INFO", "logger": "main", "message": "Processing document: handwritten.png (89671 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:39.920393", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: handwritten.png", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:39.921169", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: handwritten.png", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T11:55:41.024380", "level": "INFO", "logger": "agents", "message": "Document analysis for handwritten.png: HANDWRITTEN_MODE (confidence: 0.9)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T11:55:41.025682", "level": "INFO", "logger": "agents", "message": "Routing handwritten.png to HANDWRITTEN_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T11:55:41.026752", "level": "INFO", "logger": "agents", "message": "Processing handwritten.png in HANDWRITTEN_MODE", "module": "agents", "function": "_process_handwritten_mode", "line": 451}
{"timestamp": "2025-08-04T11:55:48.713058", "level": "INFO", "logger": "main", "message": "Document routing decision for handwritten.png:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.714738", "level": "INFO", "logger": "main", "message": "  - Processing Mode: HANDWRITTEN_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.715170", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.90", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.715649", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.716017", "level": "INFO", "logger": "main", "message": "  - Predicted Content: handwritten", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:55:48.716301", "level": "INFO", "logger": "main", "message": "  - Reasoning: The filename 'handwritten.png' explicitly contains keywords indicating handwritten content, such as 'handwritten', 'hand', and 'written'. Given that it's an image file and not a PDF, and the presence of these keywords, it's highly likely that the document contains handwritten text. This aligns with the criteria for HANDWRITTEN_MODE, which includes handwritten invoices, hand-filled forms, and handwritten notes.", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:56:29.641853", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_115629_140472e6_handwritten.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T11:56:29.642869", "level": "INFO", "logger": "main", "message": "Document processed successfully: handwritten.png -> HANDWRITTEN_MODE (49.72s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:04.931136", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:04.932142", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:04.932859", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: invoices/data2.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T12:00:06.238848", "level": "INFO", "logger": "agents", "message": "Document analysis for invoices/data2.pdf: STANDARD_MODE (confidence: 0.95)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T12:00:06.240107", "level": "INFO", "logger": "agents", "message": "Routing invoices/data2.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T12:00:06.241288", "level": "INFO", "logger": "agents", "message": "Processing invoices/data2.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T12:00:10.478525", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.479894", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.481040", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.482070", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.483065", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:10.485290", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is a strong indicator for STANDARD_MODE. The filename contains the keyword 'invoice', suggesting it is a business document, and business documents are usually printed unless specifically noted as handwritten. There are no handwriting keywords in the filename, further supporting the choice of STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:12.136776", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_120012_9cdb12f6_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:12.137971", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data2.pdf -> STANDARD_MODE (7.21s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:17.068608", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:17.069293", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:17.069800", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: invoices/data3.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T12:00:18.007632", "level": "INFO", "logger": "agents", "message": "Document analysis for invoices/data3.pdf: STANDARD_MODE (confidence: 0.95)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T12:00:18.008902", "level": "INFO", "logger": "agents", "message": "Routing invoices/data3.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T12:00:18.009995", "level": "INFO", "logger": "agents", "message": "Processing invoices/data3.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T12:00:21.208246", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.209356", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.210384", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.211326", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.212258", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:21.213145", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename 'invoices/data3.pdf' suggests it is an invoice, and the absence of handwriting keywords further supports this conclusion. Given the file type and content prediction, it is highly likely that this document is a printed or digital invoice, making STANDARD_MODE the optimal processing mode. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:25.733397", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_120025_054a386a_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:25.735553", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data3.pdf -> STANDARD_MODE (8.67s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:35.537685", "level": "INFO", "logger": "main", "message": "Processing document: invoices/data4.pdf (419197 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:35.538517", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/data4.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:35.539101", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: invoices/data4.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T12:00:36.655130", "level": "INFO", "logger": "agents", "message": "Document analysis for invoices/data4.pdf: STANDARD_MODE (confidence: 0.95)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T12:00:36.656343", "level": "INFO", "logger": "agents", "message": "Routing invoices/data4.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T12:00:36.657430", "level": "INFO", "logger": "agents", "message": "Processing invoices/data4.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T12:00:41.703790", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/data4.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.704952", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.705963", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.706821", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.707889", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:41.708944", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename 'invoices/data4.pdf' suggests a business document, likely an invoice, and does not contain any keywords indicating handwritten content. The detected keyword 'invoice' further supports the assumption that this is a printed or digital document. Given the file type and filename analysis, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:55.633028", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_120055_febc5092_data4.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:00:55.634439", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/data4.pdf -> STANDARD_MODE (20.10s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:07.233679", "level": "INFO", "logger": "main", "message": "Processing document: invoices/dataarab1.pdf (7041 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:07.236035", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: invoices/dataarab1.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:07.237112", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: invoices/dataarab1.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T12:01:08.332730", "level": "INFO", "logger": "agents", "message": "Document analysis for invoices/dataarab1.pdf: STANDARD_MODE (confidence: 0.95)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T12:01:08.334236", "level": "INFO", "logger": "agents", "message": "Routing invoices/dataarab1.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T12:01:08.335028", "level": "INFO", "logger": "agents", "message": "Processing invoices/dataarab1.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T12:01:11.391526", "level": "INFO", "logger": "main", "message": "Document routing decision for invoices/dataarab1.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.392795", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.393857", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.395218", "level": "INFO", "logger": "main", "message": "  - Document Category: invoice", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.396421", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:11.397978", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which typically contains printed or digital content. The filename does not contain any handwriting keywords, but it does contain the keyword 'invoice', suggesting a business document. Given that PDF files are almost always processed in STANDARD_MODE and business documents are usually printed, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:14.828642", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_120114_74afb029_dataarab1.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:01:14.830078", "level": "INFO", "logger": "main", "message": "Document processed successfully: invoices/dataarab1.pdf -> STANDARD_MODE (7.60s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.633142", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.956291", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.957278", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.958316", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:06:24.959293", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 36", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:09.117612", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:09.118797", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:09.119589", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: data2.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T12:18:10.373350", "level": "INFO", "logger": "agents", "message": "Document analysis for data2.pdf: STANDARD_MODE (confidence: 0.95)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T12:18:10.374784", "level": "INFO", "logger": "agents", "message": "Routing data2.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T12:18:10.376131", "level": "INFO", "logger": "agents", "message": "Processing data2.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T12:18:16.891256", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.892669", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.893655", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.894811", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.897333", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:16.898942", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, and there are no detected keywords that would suggest otherwise. Given that PDF files should almost always use STANDARD_MODE, and the absence of any indicators suggesting handwritten content, the decision to use STANDARD_MODE is clear. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:18.654234", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_121818_71d87666_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:18:18.655540", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (9.54s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.327911", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.627154", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.627710", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.628105", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T12:21:56.628584", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 6", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:41.677657", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:41.678827", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:41.679861", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: data2.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T14:05:43.027237", "level": "ERROR", "logger": "agents", "message": "Error analyzing document data2.pdf: Connection error.", "module": "agents", "function": "analyze_document", "line": 190}
{"timestamp": "2025-08-04T14:05:43.031919", "level": "INFO", "logger": "agents", "message": "Routing data2.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T14:05:43.032966", "level": "INFO", "logger": "agents", "message": "Processing data2.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T14:05:47.947031", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.948312", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.949486", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.950565", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.951661", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:47.952702", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:52.038250", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_140552_295a3f82_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:05:52.039572", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (10.36s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.118385", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.426122", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.427929", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.429066", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:28.430680", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:51.524545", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:51.525489", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:51.528190", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: data2.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T14:10:52.755274", "level": "ERROR", "logger": "agents", "message": "Error analyzing document data2.pdf: Connection error.", "module": "agents", "function": "analyze_document", "line": 190}
{"timestamp": "2025-08-04T14:10:52.756735", "level": "INFO", "logger": "agents", "message": "Routing data2.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T14:10:52.757970", "level": "INFO", "logger": "agents", "message": "Processing data2.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T14:10:57.657785", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.659808", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.661146", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.60", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.662028", "level": "INFO", "logger": "main", "message": "  - Document Category: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.662988", "level": "INFO", "logger": "main", "message": "  - Predicted Content: unknown", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:10:57.664434", "level": "INFO", "logger": "main", "message": "  - Reasoning: Default analysis due to processing error", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:11:01.724350", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_141101_3bb38f33_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:11:01.727064", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (10.20s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:22.826310", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:23.184682", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:23.185132", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:23.185995", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:12:23.186173", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 1", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:13.422007", "level": "INFO", "logger": "main", "message": "Processing document: data3.pdf (82311 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:13.423258", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data3.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:13.423816", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: data3.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-04T14:13:14.848961", "level": "INFO", "logger": "agents", "message": "Document analysis for data3.pdf: STANDARD_MODE (confidence: 0.95)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-04T14:13:14.850495", "level": "INFO", "logger": "agents", "message": "Routing data3.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-04T14:13:14.851310", "level": "INFO", "logger": "agents", "message": "Processing data3.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-04T14:13:18.167469", "level": "INFO", "logger": "main", "message": "Document routing decision for data3.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.169478", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.170675", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.171726", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.173193", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:18.174158", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is typically processed in STANDARD_MODE. The filename does not contain any keywords indicating handwritten content or invoice/receipt context. Given the file type and lack of specific indicators, it is highly likely that this document is a printed or digital document, suitable for STANDARD_MODE processing. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:20.128094", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250804_141320_fe2c43ee_data3.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-04T14:13:20.129396", "level": "INFO", "logger": "main", "message": "Document processed successfully: data3.pdf -> STANDARD_MODE (6.71s)", "module": "unified_logging", "function": "info", "line": 244}
