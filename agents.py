"""
AutoGen Multi-Agent System for Document Processing

This module implements the AutoGen agent architecture that orchestrates
document processing across different modes:
- Document Analyzer Agent: Determines processing mode
- Standard Mode Agent: Handles PDF and structured documents
- Handwritten Mode Agent: Processes handwritten images
- RAG Chatbot Agent: Provides conversational interface
"""

import os
import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import logging

import autogen
from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
from groq import Groq

from config import settings

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)

class DocumentAnalyzerAgent:
    """
    Agent responsible for analyzing documents and determining the appropriate processing mode
    """
    
    def __init__(self):
        self.groq_client = Groq(api_key=settings.groq_api_key)
        self.model = settings.autogen_analyzer_model
        
        # Configuration for direct Groq usage (AutoGen alternative)
        self.config = {
            "model": self.model,
            "api_key": settings.groq_api_key,
            "base_url": "https://api.groq.com/openai/v1",
            "api_type": "openai"
        }

        # Note: Using direct Groq client instead of AutoGen for now
        # self.agent = AssistantAgent(
        #     name="DocumentAnalyzer",
        #     system_message=self._get_system_message(),
        #     llm_config={"config_list": [self.config]},
        #     max_consecutive_auto_reply=3
        # )
    
    def _get_system_message(self) -> str:
        return """You are an Advanced Document Analysis Expert specializing in invoice and document classification for optimal processing.

Your primary responsibility is to analyze documents and determine the most appropriate processing mode:

**STANDARD_MODE** - Use for:
- PDF documents (always)
- Typed/printed invoices and receipts
- Computer-generated documents
- Scanned printed documents with clear, machine-readable text
- Digital invoices from accounting software
- Structured forms with printed text
- Documents with consistent fonts and layouts

**HANDWRITTEN_MODE** - Use for:
- Handwritten invoices and receipts
- Hand-filled forms and documents
- Sketches, notes, or manuscripts
- Documents with cursive or handwritten text
- Images of handwritten content
- Mixed documents where handwriting is predominant

**Analysis Criteria:**
1. **File Type Analysis**: PDF files → STANDARD_MODE (unless explicitly handwritten content)
2. **Filename Analysis**: Look for keywords like "handwritten", "hand", "written", "sketch", "note", "manuscript"
3. **Content Prediction**: Based on filename patterns, predict content type
4. **Document Purpose**: Invoices, receipts, forms - determine if likely handwritten or printed

**Decision Rules:**
- PDF files: 95% confidence → STANDARD_MODE
- Images with handwriting keywords: 90% confidence → HANDWRITTEN_MODE
- Images without clear indicators: Analyze filename for business context
- Invoice/receipt images: Consider source (handwritten vs printed)
- When uncertain: Default to STANDARD_MODE with lower confidence

**Response Format (JSON only):**
{
    "processing_mode": "STANDARD_MODE" or "HANDWRITTEN_MODE",
    "confidence": 0.0-1.0,
    "reasoning": "Detailed explanation of decision based on file type, filename, and predicted content",
    "file_type": "pdf" or "image",
    "characteristics": ["list", "of", "detected", "characteristics"],
    "document_category": "invoice/receipt/form/other",
    "predicted_content": "printed/handwritten/mixed"
}

Provide thorough analysis and high confidence scores for clear cases."""

    async def analyze_document(self, filename: str, file_content: bytes) -> Dict[str, Any]:
        """
        Analyze a document and determine the appropriate processing mode with enhanced intelligence
        """
        try:
            # Determine file type
            file_extension = Path(filename).suffix.lower()
            file_type = "pdf" if file_extension == ".pdf" else "image"

            # Enhanced filename analysis
            filename_lower = filename.lower()
            handwriting_keywords = [
                "handwritten", "handwriting", "hand", "written", "sketch", "note",
                "manuscript", "cursive", "scribble", "pen", "pencil", "manual"
            ]

            invoice_keywords = [
                "invoice", "receipt", "bill", "payment", "purchase", "order",
                "transaction", "statement", "voucher", "ticket"
            ]

            # Analyze filename for content hints
            has_handwriting_hints = any(keyword in filename_lower for keyword in handwriting_keywords)
            has_invoice_hints = any(keyword in filename_lower for keyword in invoice_keywords)

            # Create enhanced analysis prompt
            prompt = f"""
            Analyze this document for optimal processing mode selection:

            **Document Details:**
            - Filename: {filename}
            - File Type: {file_type}
            - File Size: {len(file_content)} bytes
            - Extension: {file_extension}

            **Filename Analysis:**
            - Contains handwriting keywords: {has_handwriting_hints}
            - Contains invoice/receipt keywords: {has_invoice_hints}
            - Detected keywords: {[kw for kw in handwriting_keywords + invoice_keywords if kw in filename_lower]}

            **Processing Mode Decision:**
            Determine if this should be processed by:
            - STANDARD_MODE: For PDFs, printed/typed invoices, digital documents, scanned printed text
            - HANDWRITTEN_MODE: For handwritten invoices, hand-filled forms, handwritten notes

            **Special Considerations:**
            - PDF files should almost always use STANDARD_MODE
            - Image files need careful analysis of filename and predicted content
            - Invoice/receipt images: Consider if likely handwritten vs printed
            - Business documents are usually printed unless specifically noted as handwritten

            Provide detailed analysis in the specified JSON format with high confidence for clear cases.
            """
            
            # Use Groq for analysis
            response = self.groq_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_message()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )
            
            # Parse response
            analysis_text = response.choices[0].message.content
            
            # Try to extract JSON from response
            try:
                # Look for JSON in the response
                import re
                json_match = re.search(r'\{.*\}', analysis_text, re.DOTALL)
                if json_match:
                    analysis = json.loads(json_match.group())
                else:
                    # Fallback parsing
                    analysis = self._parse_fallback_response(analysis_text, filename, file_type)
            except json.JSONDecodeError:
                analysis = self._parse_fallback_response(analysis_text, filename, file_type)
            
            # Validate and ensure required fields
            analysis = self._validate_analysis(analysis, filename, file_type)
            
            logger.info(f"Document analysis for {filename}: {analysis['processing_mode']} (confidence: {analysis['confidence']})")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing document {filename}: {str(e)}")
            # Return default analysis
            return self._get_default_analysis(filename, file_extension)
    
    def _parse_fallback_response(self, response_text: str, filename: str, file_type: str) -> Dict[str, Any]:
        """Enhanced fallback parsing with better intelligence"""
        response_lower = response_text.lower()
        filename_lower = filename.lower()

        # Enhanced keyword analysis
        handwritten_keywords = [
            "handwritten", "handwriting", "hand", "written", "sketch", "note",
            "manuscript", "cursive", "scribble", "pen", "pencil", "manual"
        ]

        invoice_keywords = [
            "invoice", "receipt", "bill", "payment", "purchase", "order",
            "transaction", "statement", "voucher", "ticket"
        ]

        # Check for handwriting indicators in response and filename
        response_handwriting = any(keyword in response_lower for keyword in handwritten_keywords)
        filename_handwriting = any(keyword in filename_lower for keyword in handwritten_keywords)
        filename_invoice = any(keyword in filename_lower for keyword in invoice_keywords)

        # Enhanced decision logic
        if file_type == "pdf":
            # PDFs are almost always standard mode unless explicitly handwritten
            if "handwritten" in filename_lower or "hand" in filename_lower:
                processing_mode = "HANDWRITTEN_MODE"
                confidence = 0.85
                reasoning = "PDF with handwritten content indicators in filename"
            else:
                processing_mode = "STANDARD_MODE"
                confidence = 0.95
                reasoning = "PDF file - typically contains printed/typed content"

        elif filename_handwriting or response_handwriting:
            # Strong handwriting indicators
            processing_mode = "HANDWRITTEN_MODE"
            confidence = 0.9
            reasoning = "Strong handwriting indicators in filename or analysis"

        elif filename_invoice:
            # Invoice/receipt images - need to determine if handwritten or printed
            if any(word in filename_lower for word in ["handwritten", "hand", "manual"]):
                processing_mode = "HANDWRITTEN_MODE"
                confidence = 0.85
                reasoning = "Invoice/receipt with handwriting indicators"
            else:
                processing_mode = "STANDARD_MODE"
                confidence = 0.8
                reasoning = "Invoice/receipt image - likely printed/scanned document"

        else:
            # Default to standard mode for unclear cases
            processing_mode = "STANDARD_MODE"
            confidence = 0.7
            reasoning = "Default to standard mode for unclear document type"

        # Determine document category
        if filename_invoice:
            document_category = "invoice/receipt"
        elif any(word in filename_lower for word in ["form", "application"]):
            document_category = "form"
        else:
            document_category = "other"

        # Predict content type
        if filename_handwriting or response_handwriting:
            predicted_content = "handwritten"
        else:
            predicted_content = "printed"

        return {
            "processing_mode": processing_mode,
            "confidence": confidence,
            "reasoning": reasoning,
            "file_type": file_type,
            "characteristics": [
                "fallback_analysis",
                f"filename_handwriting: {filename_handwriting}",
                f"response_handwriting: {response_handwriting}",
                f"invoice_document: {filename_invoice}"
            ],
            "document_category": document_category,
            "predicted_content": predicted_content
        }
    
    def _validate_analysis(self, analysis: Dict[str, Any], filename: str, file_type: str) -> Dict[str, Any]:
        """Enhanced validation with intelligent defaults"""
        filename_lower = filename.lower()

        # Ensure required fields exist with intelligent defaults
        if "processing_mode" not in analysis:
            # Intelligent default based on filename analysis
            if any(word in filename_lower for word in ["handwritten", "hand", "written", "sketch", "note"]):
                analysis["processing_mode"] = "HANDWRITTEN_MODE"
            else:
                analysis["processing_mode"] = "STANDARD_MODE"

        if "confidence" not in analysis:
            analysis["confidence"] = 0.7

        if "reasoning" not in analysis:
            analysis["reasoning"] = "Default analysis with filename-based routing"

        if "file_type" not in analysis:
            analysis["file_type"] = file_type

        if "characteristics" not in analysis:
            analysis["characteristics"] = ["auto_detected"]

        if "document_category" not in analysis:
            if any(word in filename_lower for word in ["invoice", "receipt", "bill"]):
                analysis["document_category"] = "invoice/receipt"
            elif any(word in filename_lower for word in ["form", "application"]):
                analysis["document_category"] = "form"
            else:
                analysis["document_category"] = "other"

        if "predicted_content" not in analysis:
            if any(word in filename_lower for word in ["handwritten", "hand", "written"]):
                analysis["predicted_content"] = "handwritten"
            else:
                analysis["predicted_content"] = "printed"

        # Validate processing mode
        if analysis["processing_mode"] not in ["STANDARD_MODE", "HANDWRITTEN_MODE"]:
            analysis["processing_mode"] = "STANDARD_MODE"

        # Ensure confidence is between 0 and 1
        try:
            confidence = float(analysis["confidence"])
            analysis["confidence"] = max(0.0, min(1.0, confidence))
        except (ValueError, TypeError):
            analysis["confidence"] = 0.7

        # Enhanced PDF handling - allow handwritten PDFs if explicitly indicated
        if file_type == "pdf":
            if "handwritten" in filename_lower or "hand" in filename_lower:
                # Keep handwritten mode for explicitly handwritten PDFs
                if analysis["processing_mode"] == "HANDWRITTEN_MODE":
                    analysis["confidence"] = max(analysis["confidence"], 0.85)
                    analysis["reasoning"] += " (PDF with handwritten content indicators)"
            else:
                # Standard PDFs go to standard mode
                analysis["processing_mode"] = "STANDARD_MODE"
                analysis["confidence"] = max(analysis["confidence"], 0.95)
                analysis["reasoning"] += " (PDF - standard processing)"

        # Boost confidence for clear handwriting indicators
        if analysis["processing_mode"] == "HANDWRITTEN_MODE":
            handwriting_indicators = ["handwritten", "hand", "written", "sketch", "note", "cursive"]
            if any(word in filename_lower for word in handwriting_indicators):
                analysis["confidence"] = max(analysis["confidence"], 0.9)

        return analysis
    
    def _get_default_analysis(self, filename: str, file_extension: str) -> Dict[str, Any]:
        """Get default analysis when all else fails"""
        file_type = "pdf" if file_extension == ".pdf" else "image"
        
        return {
            "processing_mode": "STANDARD_MODE",
            "confidence": 0.6,
            "reasoning": "Default analysis due to processing error",
            "file_type": file_type,
            "characteristics": ["default_fallback"]
        }

class AgentOrchestrator:
    """
    Main orchestrator that manages all agents and coordinates document processing
    """
    
    def __init__(self):
        self.analyzer_agent = DocumentAnalyzerAgent()
        self.processing_results = {}
        
        logger.info("AgentOrchestrator initialized successfully")
    
    async def process_document(self, filename: str, file_content: bytes) -> Dict[str, Any]:
        """
        Main entry point for document processing using AutoGen agents
        """
        try:
            # Step 1: Analyze document to determine processing mode
            logger.info(f"Starting document analysis for: {filename}")
            analysis = await self.analyzer_agent.analyze_document(filename, file_content)
            
            # Step 2: Route to appropriate processing mode
            processing_mode = analysis["processing_mode"]
            logger.info(f"Routing {filename} to {processing_mode}")
            
            # Step 3: Process document based on determined mode
            if processing_mode == "STANDARD_MODE":
                result = await self._process_standard_mode(filename, file_content, analysis)
            elif processing_mode == "HANDWRITTEN_MODE":
                result = await self._process_handwritten_mode(filename, file_content, analysis)
            else:
                raise ValueError(f"Unknown processing mode: {processing_mode}")
            
            # Step 4: Store results and return
            self.processing_results[filename] = result
            
            return {
                "success": True,
                "filename": filename,
                "analysis": analysis,
                "processing_result": result,
                "processing_mode": processing_mode
            }
            
        except Exception as e:
            logger.error(f"Error processing document {filename}: {str(e)}")
            return {
                "success": False,
                "filename": filename,
                "error": str(e),
                "processing_mode": "FAILED"
            }
    
    async def _process_standard_mode(self, filename: str, file_content: bytes, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Process document using standard mode"""
        logger.info(f"Processing {filename} in STANDARD_MODE")

        try:
            # Import here to avoid circular imports
            from core_processors import StandardModeProcessor

            # Create processor instance
            processor = StandardModeProcessor()

            # Process the document
            result = await processor.process_document(filename, file_content)

            return {
                "mode": "STANDARD_MODE",
                "status": "processed" if result.success else "failed",
                "message": "Document processed using standard mode" if result.success else result.error_message,
                "analysis": analysis,
                "processing_result": result,
                "csv_content": result.csv_content if result.success else None,
                "data": result.data if result.success else None,
                "processing_time": result.processing_time,
                "metadata": result.metadata
            }

        except Exception as e:
            logger.error(f"Error in standard mode processing: {e}")
            return {
                "mode": "STANDARD_MODE",
                "status": "failed",
                "message": f"Standard mode processing failed: {str(e)}",
                "analysis": analysis,
                "error": str(e)
            }

    async def _process_handwritten_mode(self, filename: str, file_content: bytes, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Process document using handwritten mode"""
        logger.info(f"Processing {filename} in HANDWRITTEN_MODE")

        try:
            # Import here to avoid circular imports
            from core_processors import HandwrittenModeProcessor

            # Create processor instance
            processor = HandwrittenModeProcessor()

            # Process the document
            result = await processor.process_document(filename, file_content)

            return {
                "mode": "HANDWRITTEN_MODE",
                "status": "processed" if result.success else "failed",
                "message": "Document processed using handwritten mode" if result.success else result.error_message,
                "analysis": analysis,
                "processing_result": result,
                "csv_content": result.csv_content if result.success else None,
                "data": result.data if result.success else None,
                "processing_time": result.processing_time,
                "metadata": result.metadata
            }

        except Exception as e:
            logger.error(f"Error in handwritten mode processing: {e}")
            return {
                "mode": "HANDWRITTEN_MODE",
                "status": "failed",
                "message": f"Handwritten mode processing failed: {str(e)}",
                "analysis": analysis,
                "error": str(e)
            }

# Global orchestrator instance
orchestrator = AgentOrchestrator()

# Export main functions
async def analyze_and_process_document(filename: str, file_content: bytes) -> Dict[str, Any]:
    """Main entry point for document processing"""
    return await orchestrator.process_document(filename, file_content)
