"""
Automated Invoice Extractor - Main FastAPI Application

This is the single FastAPI application that serves all endpoints and integrates
with AutoGen agents for intelligent document processing.

Endpoints:
- POST /process-document/csv - Process documents with intelligent routing
- POST /chatbot - RAG-based document chatbot
- DELETE /refresh-db - Refresh the RAG database
- GET /health - Health check
- GET / - API information
"""

import os
import sys
import asyncio
import time
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import tempfile
import io

from fastapi import FastAPI, File, UploadFile, Form, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field
import uvicorn

# Import our modules
from config import settings
from agents import analyze_and_process_document
from core_processors import StandardModeProcessor, HandwrittenModeProcessor, RAGChatbotProcessor
from unified_logging import main_logger, api_logger, log_execution_time

# Use unified logging
logger = main_logger

# ================================
# PYDANTIC MODELS
# ================================

class ChatMessage(BaseModel):
    """Chat message for RAG chatbot"""
    message: str = Field(..., description="User's question about the documents")
    session_id: Optional[str] = Field(None, description="Optional session ID for conversation tracking")

class ChatResponse(BaseModel):
    """Response from RAG chatbot"""
    success: bool
    response: str
    session_id: str
    sources: List[str]
    timestamp: str
    error: Optional[str] = None

class HealthResponse(BaseModel):
    """Health check response"""
    status: str
    timestamp: str
    version: str
    components: Dict[str, bool]
    documents_loaded: int

class ProcessingResponse(BaseModel):
    """Document processing response"""
    success: bool
    message: str
    filename: str
    processing_mode: str
    processing_time: float
    metadata: Optional[Dict[str, Any]] = None

class DatabaseRefreshResponse(BaseModel):
    """Database refresh response"""
    success: bool
    message: str
    documents_loaded: int
    timestamp: str
    csv_files_deleted: Optional[int] = 0
    vector_db_cleared: Optional[bool] = False

# ================================
# FASTAPI APPLICATION
# ================================

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description=settings.app_description,
    docs_url=settings.docs_url if settings.enable_docs else None,
    redoc_url=settings.redoc_url if settings.enable_docs else None,
    openapi_url=settings.openapi_url if settings.enable_docs else None
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# ================================
# GLOBAL PROCESSORS
# ================================

# Initialize processors
standard_processor = StandardModeProcessor()
handwritten_processor = HandwrittenModeProcessor()
rag_processor = RAGChatbotProcessor()

logger.info("All processors initialized successfully")

# ================================
# UTILITY FUNCTIONS
# ================================

def validate_file(file: UploadFile) -> None:
    """Validate uploaded file"""
    # Check file size
    if hasattr(file, 'size') and file.size and file.size > settings.max_file_size_mb * 1024 * 1024:
        raise HTTPException(
            status_code=413,
            detail=f"File too large. Maximum size: {settings.max_file_size_mb}MB"
        )
    
    # Check file extension
    if file.filename:
        file_ext = Path(file.filename).suffix.lower()
        allowed_extensions = settings.get_allowed_extensions_list()
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type: {file_ext}. Allowed: {', '.join(allowed_extensions)}"
            )

async def save_csv_result(csv_content: str, filename: str) -> str:
    """Save CSV result to results directory"""
    try:
        # Ensure results directory exists
        results_dir = Path(settings.output_dir)
        results_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        csv_filename = f"{timestamp}_{unique_id}_{Path(filename).stem}.csv"
        csv_path = results_dir / csv_filename
        
        # Save CSV content
        csv_path.write_text(csv_content, encoding='utf-8')
        
        logger.info(f"CSV result saved: {csv_path}")
        return str(csv_path)
        
    except Exception as e:
        logger.error(f"Error saving CSV result: {e}")
        raise HTTPException(status_code=500, detail=f"Error saving result: {str(e)}")

# ================================
# API ENDPOINTS
# ================================

@app.get("/", response_model=Dict[str, Any])
async def root():
    """Root endpoint with API information"""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "description": settings.app_description,
        "endpoints": {
            "process_document": "/process-document/csv",
            "chatbot": "/chatbot",
            "refresh_database": "/refresh-db",
            "health": "/health"
        },
        "features": [
            "AutoGen multi-agent orchestration",
            "Intelligent document routing",
            "Standard mode for PDFs and structured documents",
            "Handwritten mode for handwritten images",
            "RAG-based document chatbot",
            "Unified CSV output format",
            "Comprehensive logging and monitoring"
        ],
        "limits": {
            "max_file_size_mb": settings.max_file_size_mb,
            "max_concurrent_tasks": settings.max_concurrent_tasks,
            "allowed_extensions": settings.get_allowed_extensions_list()
        }
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check component health
        components = {
            "groq_api": bool(settings.groq_api_key),
            "standard_processor": bool(standard_processor),
            "handwritten_processor": bool(handwritten_processor),
            "rag_processor": bool(rag_processor),
            "vector_store": bool(rag_processor.vector_store),
            "llm": bool(rag_processor.llm),
            "embeddings": bool(rag_processor.embeddings)
        }
        
        # Determine overall status
        all_healthy = all(components.values())
        status = "healthy" if all_healthy else "degraded"
        
        return HealthResponse(
            status=status,
            timestamp=datetime.now().isoformat(),
            version=settings.app_version,
            components=components,
            documents_loaded=rag_processor.documents_loaded
        )
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return HealthResponse(
            status="unhealthy",
            timestamp=datetime.now().isoformat(),
            version=settings.app_version,
            components={},
            documents_loaded=0
        )

@app.post("/process-document/csv", response_model=ProcessingResponse)
async def process_document_csv(
    file: UploadFile = File(..., description="Document file to process"),
    enable_fallback: bool = Form(True, description="Enable fallback to alternative processing mode")
):
    """
    Process document using AutoGen agents with intelligent routing
    
    This endpoint:
    1. Analyzes the document using AutoGen DocumentAnalyzer agent
    2. Routes to appropriate processing mode (Standard or Handwritten)
    3. Processes the document and extracts structured data
    4. Returns CSV content and saves to results directory
    5. Automatically refreshes RAG database for immediate chatbot availability
    """
    start_time = time.time()
    
    try:
        # Validate file
        validate_file(file)
        
        # Read file content
        file_content = await file.read()
        filename = file.filename or "unknown_file"
        
        logger.info(f"Processing document: {filename} ({len(file_content)} bytes)")
        
        # Use AutoGen agents for intelligent processing
        logger.info(f"Starting intelligent document analysis for: {filename}")
        result = await analyze_and_process_document(filename, file_content)

        if not result["success"]:
            logger.error(f"Document analysis failed for {filename}: {result.get('error', 'Unknown error')}")
            raise HTTPException(
                status_code=500,
                detail=f"Processing failed: {result.get('error', 'Unknown error')}"
            )

        # Get processing result and analysis
        processing_result = result["processing_result"]
        analysis = result["analysis"]
        processing_mode = result["processing_mode"]

        # Log the routing decision
        logger.info(f"Document routing decision for {filename}:")
        logger.info(f"  - Processing Mode: {processing_mode}")
        logger.info(f"  - Confidence: {analysis.get('confidence', 0):.2f}")
        logger.info(f"  - Document Category: {analysis.get('document_category', 'unknown')}")
        logger.info(f"  - Predicted Content: {analysis.get('predicted_content', 'unknown')}")
        logger.info(f"  - Reasoning: {analysis.get('reasoning', 'No reasoning provided')}")
        
        # Process with appropriate processor based on analysis
        if processing_mode == "STANDARD_MODE":
            processor_result = await standard_processor.process_document(filename, file_content)
        elif processing_mode == "HANDWRITTEN_MODE":
            processor_result = await handwritten_processor.process_document(filename, file_content)
        else:
            raise HTTPException(status_code=500, detail=f"Unknown processing mode: {processing_mode}")
        
        if not processor_result.success:
            # Try fallback if enabled
            if enable_fallback:
                logger.info(f"Primary processing failed, trying fallback mode")
                fallback_mode = "HANDWRITTEN_MODE" if processing_mode == "STANDARD_MODE" else "STANDARD_MODE"
                
                if fallback_mode == "STANDARD_MODE":
                    processor_result = await standard_processor.process_document(filename, file_content)
                else:
                    processor_result = await handwritten_processor.process_document(filename, file_content)
                
                if processor_result.success:
                    processing_mode = fallback_mode
                    logger.info(f"Fallback processing successful with {fallback_mode}")
        
        if not processor_result.success:
            raise HTTPException(
                status_code=500,
                detail=f"Processing failed: {processor_result.error_message}"
            )
        
        # Save CSV result
        csv_path = await save_csv_result(processor_result.csv_content, filename)

        # Update RAG database in background (without deleting CSV files)
        asyncio.create_task(rag_processor.update_rag_database())
        
        processing_time = time.time() - start_time
        
        # Return CSV as streaming response
        def generate_csv():
            yield processor_result.csv_content
        
        # Prepare metadata
        metadata = {
            "analysis": analysis,
            "processing_mode": processing_mode,
            "csv_saved_to": csv_path,
            "rag_refresh_triggered": True,
            **(processor_result.metadata or {})
        }
        
        # Return response with CSV content and enhanced headers
        response = StreamingResponse(
            generate_csv(),
            media_type="text/csv",
            headers={
                "Content-Disposition": f"attachment; filename={Path(filename).stem}_processed.csv",
                "X-Processing-Mode": processing_mode,
                "X-Processing-Time": str(processing_time),
                "X-Analysis-Confidence": str(analysis.get("confidence", 0)),
                "X-Document-Category": analysis.get("document_category", "unknown"),
                "X-Predicted-Content": analysis.get("predicted_content", "unknown"),
                "X-File-Type": analysis.get("file_type", "unknown"),
                "X-Routing-Reasoning": analysis.get("reasoning", "No reasoning provided")[:200],  # Truncate for header
                "X-CSV-Saved-To": csv_path,
                "X-Fallback-Used": str(processing_mode != result["analysis"]["processing_mode"]) if "analysis" in result else "false"
            }
        )
        
        logger.info(f"Document processed successfully: {filename} -> {processing_mode} ({processing_time:.2f}s)")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing document: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/chatbot", response_model=ChatResponse)
async def chat_with_documents(chat_message: ChatMessage):
    """
    Chat with processed documents using RAG
    
    This endpoint provides conversational access to all processed documents
    using Retrieval-Augmented Generation (RAG) with ChromaDB and Groq LLM.
    """
    try:
        # Process chat message
        result = await rag_processor.chat(chat_message.message, chat_message.session_id)
        
        return ChatResponse(
            success=result["success"],
            response=result["response"],
            session_id=result["session_id"],
            sources=result["sources"],
            timestamp=result.get("timestamp", datetime.now().isoformat()),
            error=result.get("error")
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        return ChatResponse(
            success=False,
            response=f"Sorry, I encountered an error: {str(e)}",
            session_id=chat_message.session_id or "unknown",
            sources=[],
            timestamp=datetime.now().isoformat(),
            error=str(e)
        )

@app.delete("/refresh-db", response_model=DatabaseRefreshResponse)
async def refresh_database():
    """
    Complete database refresh - DELETE ALL DATA and prepare for new documents

    This endpoint performs a complete cleanup:
    1. Deletes ALL CSV files from results directory and search directories
    2. Completely clears the vector database (ChromaDB)
    3. Resets the system for fresh document processing

    WARNING: This will permanently delete all processed document data!
    Use this when you want to start fresh with new documents.
    """
    try:
        logger.info("Starting complete database refresh - deleting all CSV files and clearing vector DB")

        result = await rag_processor.refresh_database()

        # Log the refresh operation details
        if result["success"]:
            logger.info(f"Database refresh completed successfully:")
            logger.info(f"  - CSV files deleted: {result.get('csv_files_deleted', 0)}")
            logger.info(f"  - Vector DB cleared: {result.get('vector_db_cleared', False)}")
            logger.info(f"  - Documents loaded: {result.get('documents_loaded', 0)}")
        else:
            logger.error(f"Database refresh failed: {result.get('message', 'Unknown error')}")

        return DatabaseRefreshResponse(
            success=result["success"],
            message=result["message"],
            documents_loaded=result.get("documents_loaded", 0),
            csv_files_deleted=result.get("csv_files_deleted", 0),
            vector_db_cleared=result.get("vector_db_cleared", False),
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        logger.error(f"Database refresh error: {e}")
        return DatabaseRefreshResponse(
            success=False,
            message=f"Error refreshing database: {str(e)}",
            documents_loaded=0,
            csv_files_deleted=0,
            vector_db_cleared=False,
            timestamp=datetime.now().isoformat()
        )

@app.get("/database-status")
async def get_database_status():
    """Get current RAG database status"""
    try:
        return rag_processor.get_database_status()
    except Exception as e:
        logger.error(f"Error getting database status: {e}")
        return {"status": "error", "error": str(e)}

# ================================
# STARTUP AND SHUTDOWN
# ================================

@app.on_event("startup")
async def startup_event():
    """Application startup"""
    logger.info(f"Starting {settings.app_name} v{settings.app_version}")
    logger.info(f"Server: {settings.host}:{settings.port}")
    logger.info(f"Debug mode: {settings.debug}")
    logger.info(f"Groq API configured: {bool(settings.groq_api_key)}")
    logger.info("Application startup complete")

@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown"""
    logger.info("Application shutdown")

# ================================
# MAIN ENTRY POINT
# ================================

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower()
    )
