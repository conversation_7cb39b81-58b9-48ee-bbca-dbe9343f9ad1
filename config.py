"""
Unified Configuration Module for Agentic Document Processing System

This module provides centralized configuration management for all components:
- AutoGen agents
- Standard mode processing
- Handwritten mode processing  
- RAG chatbot
- FastAPI server
"""

import os
from typing import List, Optional, Dict, Any
from pathlib import Path
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Unified settings for the entire agentic document processing system"""
    
    # ================================
    # CORE API CONFIGURATION
    # ================================
    groq_api_key: str = Field(..., env="GROQ_API_KEY", description="Groq API key for all LLM operations")
    app_name: str = Field(default="Agentic Document Processing System", env="APP_NAME")
    app_version: str = Field(default="2.0.0", env="APP_VERSION")
    app_description: str = Field(default="Unified AI-powered document processing with AutoGen multi-agent orchestration", env="APP_DESCRIPTION")
    
    # ================================
    # SERVER CONFIGURATION
    # ================================
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    debug: bool = Field(default=False, env="DEBUG")
    reload: bool = Field(default=True, env="RELOAD")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # ================================
    # AUTOGEN CONFIGURATION
    # ================================
    autogen_analyzer_model: str = Field(default="meta-llama/llama-3.1-8b-instant", env="AUTOGEN_ANALYZER_MODEL")
    autogen_standard_model: str = Field(default="meta-llama/llama-3.1-8b-instant", env="AUTOGEN_STANDARD_MODEL")
    autogen_handwritten_model: str = Field(default="llama-3.2-90b-vision-preview", env="AUTOGEN_HANDWRITTEN_MODEL")
    autogen_rag_model: str = Field(default="llama3-8b-8192", env="AUTOGEN_RAG_MODEL")
    
    max_agent_rounds: int = Field(default=5, env="MAX_AGENT_ROUNDS")
    agent_timeout_seconds: int = Field(default=300, env="AGENT_TIMEOUT_SECONDS")
    enable_agent_logging: bool = Field(default=True, env="ENABLE_AGENT_LOGGING")
    
    # ================================
    # DOCUMENT PROCESSING CONFIGURATION
    # ================================
    max_file_size_mb: int = Field(default=50, env="MAX_FILE_SIZE_MB")
    max_batch_files: int = Field(default=50, env="MAX_BATCH_FILES")
    allowed_extensions: str = Field(default=".pdf,.jpg,.jpeg,.png,.bmp,.tiff,.tif,.txt,.webp,.gif", env="ALLOWED_EXTENSIONS")
    
    processing_timeout_seconds: int = Field(default=300, env="PROCESSING_TIMEOUT_SECONDS")
    max_concurrent_tasks: int = Field(default=5, env="MAX_CONCURRENT_TASKS")
    cleanup_temp_files: bool = Field(default=True, env="CLEANUP_TEMP_FILES")
    
    # ================================
    # STANDARD MODE CONFIGURATION
    # ================================
    tesseract_config: str = Field(default="--oem 3 --psm 6", env="TESSERACT_CONFIG")
    enable_ocr_fallback: bool = Field(default=True, env="ENABLE_OCR_FALLBACK")
    preserve_layout: bool = Field(default=True, env="PRESERVE_LAYOUT")
    
    default_language: str = Field(default="en", env="DEFAULT_LANGUAGE")
    auto_detect_language: bool = Field(default=True, env="AUTO_DETECT_LANGUAGE")
    supported_languages: str = Field(default="en,es,fr,de,it,pt,ru,zh,ja,ko,ar,hi", env="SUPPORTED_LANGUAGES")
    
    # ================================
    # HANDWRITTEN MODE CONFIGURATION
    # ================================
    default_sections: int = Field(default=3, env="DEFAULT_SECTIONS")
    max_sections: int = Field(default=6, env="MAX_SECTIONS")
    section_overlap: float = Field(default=0.2, env="SECTION_OVERLAP")
    image_enhancement: bool = Field(default=True, env="IMAGE_ENHANCEMENT")
    
    handwritten_confidence_threshold: float = Field(default=0.7, env="HANDWRITTEN_CONFIDENCE_THRESHOLD")
    enable_preprocessing: bool = Field(default=True, env="ENABLE_PREPROCESSING")
    noise_reduction: bool = Field(default=True, env="NOISE_REDUCTION")
    
    # ================================
    # RAG CHATBOT CONFIGURATION
    # ================================
    rag_llm_model: str = Field(default="llama3-8b-8192", env="RAG_LLM_MODEL")
    rag_temperature: float = Field(default=0.1, env="RAG_TEMPERATURE")
    rag_max_tokens: int = Field(default=1024, env="RAG_MAX_TOKENS")
    
    embedding_model: str = Field(default="sentence-transformers/all-mpnet-base-v2", env="EMBEDDING_MODEL")
    embedding_device: str = Field(default="cpu", env="EMBEDDING_DEVICE")
    normalize_embeddings: bool = Field(default=True, env="NORMALIZE_EMBEDDINGS")
    
    vector_store_path: str = Field(default="./chroma_db", env="VECTOR_STORE_PATH")
    vector_store_type: str = Field(default="chromadb", env="VECTOR_STORE_TYPE")
    chunk_size: int = Field(default=1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="CHUNK_OVERLAP")
    max_retrieval_docs: int = Field(default=5, env="MAX_RETRIEVAL_DOCS")
    
    csv_results_dir: str = Field(default="./results", env="CSV_RESULTS_DIR")
    additional_search_dirs: str = Field(default="./uploads,./downloads,./data", env="ADDITIONAL_SEARCH_DIRS")
    
    # ================================
    # DIRECTORY CONFIGURATION
    # ================================
    input_dir: str = Field(default="./uploads", env="INPUT_DIR")
    output_dir: str = Field(default="./results", env="OUTPUT_DIR")
    temp_dir: str = Field(default="./temp", env="TEMP_DIR")
    logs_dir: str = Field(default="./logs", env="LOGS_DIR")
    
    processing_logs_dir: str = Field(default="./processing_logs", env="PROCESSING_LOGS_DIR")
    enable_detailed_logging: bool = Field(default=True, env="ENABLE_DETAILED_LOGGING")
    log_retention_days: int = Field(default=30, env="LOG_RETENTION_DAYS")
    
    # ================================
    # CORS CONFIGURATION
    # ================================
    cors_origins: List[str] = Field(default=["*"], env="CORS_ORIGINS")
    cors_allow_credentials: bool = Field(default=True, env="CORS_ALLOW_CREDENTIALS")
    cors_allow_methods: List[str] = Field(default=["*"], env="CORS_ALLOW_METHODS")
    cors_allow_headers: List[str] = Field(default=["*"], env="CORS_ALLOW_HEADERS")
    
    # ================================
    # SECURITY CONFIGURATION
    # ================================
    require_api_key: bool = Field(default=False, env="REQUIRE_API_KEY")
    api_key_header: str = Field(default="X-API-Key", env="API_KEY_HEADER")
    allowed_api_keys: str = Field(default="", env="ALLOWED_API_KEYS")
    
    rate_limit_enabled: bool = Field(default=False, env="RATE_LIMIT_ENABLED")
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=3600, env="RATE_LIMIT_WINDOW")
    
    # ================================
    # API DOCUMENTATION
    # ================================
    docs_url: str = Field(default="/docs", env="DOCS_URL")
    redoc_url: str = Field(default="/redoc", env="REDOC_URL")
    openapi_url: str = Field(default="/openapi.json", env="OPENAPI_URL")
    enable_docs: bool = Field(default=True, env="ENABLE_DOCS")
    
    # ================================
    # DATABASE CONFIGURATION
    # ================================
    chroma_persist_directory: str = Field(default="./chroma_db", env="CHROMA_PERSIST_DIRECTORY")
    chroma_collection_name: str = Field(default="document_embeddings", env="CHROMA_COLLECTION_NAME")
    enable_db_refresh: bool = Field(default=True, env="ENABLE_DB_REFRESH")
    
    # ================================
    # ADVANCED CONFIGURATION
    # ================================
    enable_fallback_routing: bool = Field(default=True, env="ENABLE_FALLBACK_ROUTING")
    fallback_timeout: int = Field(default=30, env="FALLBACK_TIMEOUT")
    max_fallback_attempts: int = Field(default=2, env="MAX_FALLBACK_ATTEMPTS")
    
    enable_caching: bool = Field(default=True, env="ENABLE_CACHING")
    cache_ttl_seconds: int = Field(default=3600, env="CACHE_TTL_SECONDS")
    cache_max_size: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    batch_size: int = Field(default=10, env="BATCH_SIZE")
    batch_timeout: int = Field(default=600, env="BATCH_TIMEOUT")
    enable_parallel_processing: bool = Field(default=True, env="ENABLE_PARALLEL_PROCESSING")
    
    # ================================
    # DEVELOPMENT CONFIGURATION
    # ================================
    development_mode: bool = Field(default=False, env="DEVELOPMENT_MODE")
    enable_debug_endpoints: bool = Field(default=False, env="ENABLE_DEBUG_ENDPOINTS")
    mock_external_apis: bool = Field(default=False, env="MOCK_EXTERNAL_APIS")
    
    test_mode: bool = Field(default=False, env="TEST_MODE")
    test_data_dir: str = Field(default="./test_data", env="TEST_DATA_DIR")
    enable_test_logging: bool = Field(default=False, env="ENABLE_TEST_LOGGING")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"  # Allow extra fields from .env
        
    def get_allowed_extensions_list(self) -> List[str]:
        """Get allowed file extensions as a list"""
        return [ext.strip() for ext in self.allowed_extensions.split(",")]
    
    def get_supported_languages_list(self) -> List[str]:
        """Get supported languages as a list"""
        return [lang.strip() for lang in self.supported_languages.split(",")]
    
    def get_additional_search_dirs_list(self) -> List[str]:
        """Get additional search directories as a list"""
        return [dir.strip() for dir in self.additional_search_dirs.split(",")]
    
    def get_allowed_api_keys_list(self) -> List[str]:
        """Get allowed API keys as a list"""
        if not self.allowed_api_keys:
            return []
        return [key.strip() for key in self.allowed_api_keys.split(",")]
    
    def ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [
            self.input_dir,
            self.output_dir,
            self.temp_dir,
            self.logs_dir,
            self.processing_logs_dir,
            self.csv_results_dir,
            self.vector_store_path,
            self.chroma_persist_directory
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

# Global settings instance
settings = Settings()

# Ensure directories exist on import
settings.ensure_directories()
