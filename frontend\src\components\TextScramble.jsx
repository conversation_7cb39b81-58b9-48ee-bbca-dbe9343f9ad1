import React, { useEffect, useRef } from 'react';

class TextScrambleEffect {
  constructor(el) {
    this.el = el;
    this.chars = '!<>-_\\/[]{}—=+*^?#________';
    this.update = this.update.bind(this);
  }

  setText(newText) {
    const oldText = this.el.innerText;
    const length = Math.max(oldText.length, newText.length);
    const promise = new Promise((resolve) => this.resolve = resolve);
    this.queue = [];
    
    for (let i = 0; i < length; i++) {
      const from = oldText[i] || '';
      const to = newText[i] || '';
      const start = Math.floor(Math.random() * 40);
      const end = start + Math.floor(Math.random() * 40);
      this.queue.push({ from, to, start, end });
    }
    
    cancelAnimationFrame(this.frameRequest);
    this.frame = 0;
    this.update();
    return promise;
  }

  update() {
    let output = '';
    let complete = 0;
    
    for (let i = 0, n = this.queue.length; i < n; i++) {
      let { from, to, start, end, char } = this.queue[i];
      
      if (this.frame >= end) {
        complete++;
        output += to;
      } else if (this.frame >= start) {
        if (!char || Math.random() < 0.28) {
          char = this.randomChar();
          this.queue[i].char = char;
        }
        output += `<span class="text-scramble-dud">${char}</span>`;
      } else {
        output += from;
      }
    }
    
    this.el.innerHTML = output;
    
    if (complete === this.queue.length) {
      this.resolve();
    } else {
      this.frameRequest = requestAnimationFrame(this.update);
      this.frame++;
    }
  }

  randomChar() {
    return this.chars[Math.floor(Math.random() * this.chars.length)];
  }
}

const TextScramble = ({ phrases, className = "", delay = 2000 }) => {
  const textRef = useRef(null);
  const scrambleRef = useRef(null);
  const counterRef = useRef(0);

  useEffect(() => {
    if (!textRef.current || !phrases.length) return;

    scrambleRef.current = new TextScrambleEffect(textRef.current);

    const next = () => {
      scrambleRef.current.setText(phrases[counterRef.current]).then(() => {
        setTimeout(next, delay);
      });
      counterRef.current = (counterRef.current + 1) % phrases.length;
    };

    // Start the animation
    next();

    // Cleanup
    return () => {
      if (scrambleRef.current && scrambleRef.current.frameRequest) {
        cancelAnimationFrame(scrambleRef.current.frameRequest);
      }
    };
  }, [phrases, delay]);

  return (
    <div 
      ref={textRef} 
      className={`text-scramble ${className}`}
    />
  );
};

export default TextScramble;
