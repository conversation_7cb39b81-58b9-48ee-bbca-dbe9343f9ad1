@echo off
echo ========================================
echo  UNIFIED AGENTIC DOCUMENT PROCESSOR
echo ========================================
echo.

REM Set the base directory to the current script location
set BASE_DIR=%~dp0
cd /d "%BASE_DIR%"

echo  Base directory: %BASE_DIR%
echo.

REM Check if main.py exists
if not exist "main.py" (
    echo main.py not found in current directory
    echo Please ensure you're running this from the correct directory
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo  .env file not found
    echo Please create a .env file with your GROQ_API_KEY
    echo Example: GROQ_API_KEY=your_api_key_here
    pause
    exit /b 1
)

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo  Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    pause
    exit /b 1
)


REM Start the main.py server
python main.py

REM If we reach here, the server has stopped
echo.
echo Server has stopped.
echo.
pause
