"""
Handwritten OCR Processing Module using Groq Vision API

This module provides advanced handwriting recognition capabilities using Groq's vision models.
It handles image preprocessing, text extraction, and structured data parsing for handwritten
documents, forms, and invoices.

Key Features:
- Advanced image preprocessing and enhancement
- Multi-section processing for large documents
- Handwriting recognition using Groq vision models
- Intelligent document area segmentation
- Quality assessment and optimization recommendations
- Structured data extraction from recognized text
- Support for various handwriting styles and languages
- Confidence scoring and error handling

Processing Pipeline:
1. Image loading and validation
2. Preprocessing (contrast, sharpness, noise reduction)
3. Section-based splitting for optimal recognition
4. Groq vision model text extraction
5. Text consolidation and cleanup
6. Structured data extraction using LLM
7. CSV format generation with metadata
"""

# Standard library imports for core functionality
import os  # Operating system interface for environment variables
import json  # JSON data handling for configuration and responses
import csv  # CSV file operations for data output
import re  # Regular expressions for text processing
from datetime import datetime  # Date/time operations for timestamps
from pathlib import Path  # Modern path handling utilities
from typing import List, Dict, Optional, Any  # Type hints for better code clarity
from PIL import Image, ImageEnhance, ImageFilter  # Python Imaging Library for image processing
from langchain_groq import ChatGroq  # Groq LLM integration for text processing
import base64  # Base64 encoding for image data transmission
import io  # Input/output operations for in-memory file handling
from dotenv import load_dotenv  # Environment variable management
from pydantic import BaseModel, Field, field_validator  # Data validation and serialization
from decimal import Decimal  # Precise decimal arithmetic for financial calculations

# Load environment variables from .env file
load_dotenv()


# Pydantic data models for structured document representation
class InvoiceItem(BaseModel):
    """
    Model representing a single line item in an invoice.
    Contains all relevant information about a product or service.
    """
    name: str = Field(default="", description="Item name")
    description: str = Field(default="", description="Item description")
    category: str = Field(default="", description="Item category")
    quantity: float = Field(default=0.0, description="Quantity")
    unit: str = Field(default="", description="Unit of measurement")
    unit_price: float = Field(default=0.0, description="Unit price")
    total: float = Field(default=0.0, description="Item total")

class InvoiceData(BaseModel):
    """
    Comprehensive model representing a complete invoice document.
    Contains all structured data extracted from handwritten invoices.
    """
    
    invoice_number: str = Field(default="", description="Invoice number")
    invoice_date: str = Field(default="", description="Invoice date")
    due_date: str = Field(default="", description="Due date")
    purchase_order_number: str = Field(default="", description="Purchase order number")
    reference_number: str = Field(default="", description="Reference number")

    
    vendor_name: str = Field(default="", description="Vendor name")
    vendor_address: str = Field(default="", description="Vendor address")
    vendor_phone: str = Field(default="", description="Vendor phone")
    vendor_email: str = Field(default="", description="Vendor email")
    vendor_website: str = Field(default="", description="Vendor website")
    vendor_tax_id: str = Field(default="", description="Vendor tax ID")

    
    customer_name: str = Field(default="", description="Customer name")
    customer_address: str = Field(default="", description="Customer address")
    customer_phone: str = Field(default="", description="Customer phone")
    customer_email: str = Field(default="", description="Customer email")
    customer_tax_id: str = Field(default="", description="Customer tax ID")

    
    bill_to_name: str = Field(default="", description="Bill to name")
    bill_to_address: str = Field(default="", description="Bill to address")
    ship_to_name: str = Field(default="", description="Ship to name")
    ship_to_address: str = Field(default="", description="Ship to address")

    
    currency: str = Field(default="USD", description="Currency")
    subtotal: float = Field(default=0.0, description="Subtotal")
    tax_type: str = Field(default="", description="Tax type")
    tax_rate: float = Field(default=0.0, description="Tax rate")
    tax_amount: float = Field(default=0.0, description="Tax amount")
    discount_amount: float = Field(default=0.0, description="Discount amount")
    shipping_amount: float = Field(default=0.0, description="Shipping amount")
    handling_fee: float = Field(default=0.0, description="Handling fee")
    total: float = Field(default=0.0, description="Total amount")
    amount_paid: float = Field(default=0.0, description="Amount paid")
    balance_due: float = Field(default=0.0, description="Balance due")

    
    payment_terms: str = Field(default="", description="Payment terms")
    payment_method: str = Field(default="", description="Payment method")
    payment_due_date: str = Field(default="", description="Payment due date")

    
    project_name: str = Field(default="", description="Project name")
    description: str = Field(default="", description="Description")
    notes: str = Field(default="", description="Notes")
    terms_conditions: str = Field(default="", description="Terms and conditions")

    
    items: List[InvoiceItem] = Field(default_factory=list, description="Invoice items")

    
    signatures: str = Field(default="", description="Signatures")
    handwritten_notes: str = Field(default="", description="Handwritten notes")
    stamps_seals: str = Field(default="", description="Stamps and seals")
    other_information: str = Field(default="", description="Other information")

    @field_validator('invoice_date', 'due_date', 'payment_due_date')
    @classmethod
    def validate_dates(cls, v):
        if v and v.strip():
            
            try:
                
                date_patterns = [
                    r'\d{4}-\d{2}-\d{2}',  
                    r'\d{2}/\d{2}/\d{4}',  
                    r'\d{2}-\d{2}-\d{4}',  
                    r'\d{1,2}/\d{1,2}/\d{4}',  
                ]
                for pattern in date_patterns:
                    if re.match(pattern, v.strip()):
                        return v.strip()
                return v.strip()
            except:
                return v.strip()
        return v

    @field_validator('subtotal', 'tax_amount', 'discount_amount', 'shipping_amount', 'handling_fee', 'total', 'amount_paid', 'balance_due', 'tax_rate')
    @classmethod
    def validate_numbers(cls, v):
        if isinstance(v, (int, float)):
            return float(v)
        if isinstance(v, str) and v.strip():
            try:
                
                cleaned = re.sub(r'[^\d.-]', '', v.strip())
                return float(cleaned) if cleaned else 0.0
            except:
                return 0.0
        return 0.0

class HandwrittenOCR:
    """
    Advanced Handwritten OCR Processing Engine

    This class provides comprehensive handwriting recognition capabilities using Groq's
    vision models. It handles the complete pipeline from image preprocessing to
    structured data extraction.

    Key Features:
    - Image preprocessing and enhancement
    - Multi-section processing for large documents
    - Groq vision model integration for text extraction
    - Intelligent document parsing and data extraction
    - Quality assessment and optimization
    - CSV output generation with metadata

    Processing Pipeline:
    1. Image validation and preprocessing
    2. Section-based splitting for optimal recognition
    3. Text extraction using Groq vision models
    4. Text consolidation and cleanup
    5. Structured data extraction using LLM
    6. CSV format generation
    """

    def __init__(self):
        """
        Initialize the HandwrittenOCR processor with Groq API configuration.

        Sets up the vision and text models for processing handwritten documents.
        Validates API key availability and configures model parameters.
        """
        self.groq_api_key = os.getenv("GROQ_API_KEY")  # Groq API key for authentication
        self.vision_model = "meta-llama/llama-4-maverick-17b-128e-instruct"  # Vision model for image analysis
        self.text_model = "llama-3.3-70b-versatile"  # Text model for structured data extraction
        
        
        self.input_dir = Path("input_images")
        self.output_dir = Path("output")
        self.input_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"✅ Initialized HandwrittenOCR")
        print(f"📁 Input directory: {self.input_dir.absolute()}")
        print(f"📁 Output directory: {self.output_dir.absolute()}")

    def encode_image_pil(self, image: Image.Image) -> str:
        """Convert PIL Image to base64 string"""
        buffered = io.BytesIO()
        image = image.convert("RGB")
        image.save(buffered, format="JPEG", quality=95)
        return base64.b64encode(buffered.getvalue()).decode("utf-8")

    def preprocess_image(self, image: Image.Image) -> Image.Image:
        """Enhance image for better OCR results"""
        
        if image.mode != 'L':
            image = image.convert('L')
        
        
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.5)
        
        
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(2.0)
        
        
        image = image.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        return image.convert('RGB')

    def split_image_into_sections(self, image: Image.Image, sections: int = 4, overlap: float = 0.15) -> List[Image.Image]:
        """Split image into overlapping horizontal sections for better processing"""
        width, height = image.size
        section_height = height // sections
        overlap_height = int(section_height * overlap)
        
        sections_list = []
        for i in range(sections):
            upper = max(i * section_height - overlap_height, 0)
            lower = min((i + 1) * section_height + overlap_height, height)
            section = image.crop((0, upper, width, lower))
            sections_list.append(section)
        
        return sections_list

    def extract_handwritten_text(self, image: Image.Image) -> str:
        """Extract handwritten text from image using Groq Vision API"""
        groq_llm = ChatGroq(
            groq_api_key=self.groq_api_key,
            model_name=self.vision_model,
            temperature=0
        )

        image_data_url = f"data:image/jpeg;base64,{self.encode_image_pil(image)}"

        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text", 
                        "text": (
                            "You are an expert in reading handwritten text. Please carefully analyze this image and extract ALL handwritten text you can see. "
                            "Pay special attention to:\n"
                            "1. Cursive and script handwriting\n"
                            "2. Print handwriting\n"
                            "3. Numbers and dates\n"
                            "4. Signatures and names\n"
                            "5. Any annotations or notes\n\n"
                            "Please transcribe the text exactly as written, maintaining the original structure and line breaks. "
                            "If text is unclear, indicate with [unclear] but still provide your best interpretation. "
                            "If you see printed text mixed with handwritten text, clearly distinguish between them."
                        )
                    },
                    {"type": "image_url", "image_url": {"url": image_data_url}}
                ]
            }
        ]

        try:
            response = groq_llm.invoke(messages)
            return response.content.strip()
        except Exception as e:
            print(f"❌ Error in OCR processing: {str(e)}")
            return f"Error processing image: {str(e)}"

    def consolidate_text_sections(self, text_sections: List[str]) -> str:
        """Consolidate overlapping text sections into coherent document"""
        if not text_sections:
            return ""
        
        if len(text_sections) == 1:
            return text_sections[0]

        groq_llm = ChatGroq(
            groq_api_key=self.groq_api_key,
            model_name=self.text_model,
            temperature=0
        )

        combined_text = "\n\n--- SECTION BREAK ---\n\n".join(text_sections)

        messages = [
            {
                "role": "user",
                "content": (
                    "You are provided with multiple text extractions from overlapping sections of a handwritten document. "
                    "Some sections may contain duplicate or partially repeated content due to overlaps.\n\n"
                    "Your task is to:\n"
                    "1. Identify and remove duplicate content while preserving the complete text\n"
                    "2. Maintain the original structure and flow of the handwritten document\n"
                    "3. Resolve any conflicts by choosing the most complete/accurate version\n"
                    "4. Preserve line breaks and paragraph structure\n"
                    "5. Keep any formatting indicators like [unclear] if present\n\n"
                    "Return only the consolidated, clean text without any additional comments.\n\n"
                    "Text sections to consolidate:\n\n" + combined_text
                )
            }
        ]

        try:
            response = groq_llm.invoke(messages)
            consolidated_text = response.content.strip()

            
            if not consolidated_text:
                print("⚠️  Text consolidation returned empty response, using fallback")
                return "\n\n".join(text_sections)

            print("✅ Text consolidation successful")
            return consolidated_text
        except Exception as e:
            print(f"❌ Error in text consolidation: {str(e)}")
            print("🔄 Using fallback: simple text joining")
            return "\n\n".join(text_sections)  

    def extract_invoice_data(self, extracted_text: str) -> Dict:
        """Extract structured invoice data using LLM only"""
        print("📊 Starting LLM-only invoice data extraction...")

        
        print("🤖 Running LLM extraction...")
        llm_data = self._extract_with_llm(extracted_text)

        
        if not llm_data:
            print("⚠️  LLM extraction failed, using default structure")
            return self._get_default_invoice_structure()
        else:
            print(f"✅ LLM extraction found {len([k for k, v in llm_data.items() if v])} fields")

        
        try:
            
            result = self._get_default_invoice_structure()

            
            for key, value in llm_data.items():
                if value and str(value).strip() and str(value) != "0":
                    result[key] = value

            print("✅ LLM-only invoice data extraction completed successfully")
            return result
        except Exception as e:
            print(f"❌ Error processing LLM data: {str(e)}")
            print("🔄 Using default structure")
            return self._get_default_invoice_structure()

    def _extract_with_rules(self, text: str) -> Dict:
        """Extract data using advanced rule-based patterns for high accuracy"""
        data = {}
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        
        patterns = {
            'phone': r'\(?\d{2,3}\)?\s*\d{3}[-.\s]?\d{4}',
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            'amount': r'\$\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
            'date': r'\d{1,2}[/-]\d{1,2}[/-]\d{4}',
            'license_number': r'(?:licence|license|practitioner\'?s)\s*no\.?\s*:?\s*(\d+)',
            'address': r'\d+\s+[A-Za-z\s]+(?:street|st|avenue|ave|road|rd|drive|dr|lane|ln)',
            'street_name': r'[A-Z][a-z]+\s+Street',
            'doctor_title': r'Dr\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s+[A-Z]\.?[A-Z]\.?[A-Z]\.?[A-Z]\.?)*',
            'qualification': r'M\.B\.Ch\.B\.?|Dip\.?\s+[A-Za-z\s\.]+|B\.A\.?|M\.D\.?|Ph\.D\.?'
        }

        
        for line in lines:
            
            doctor_match = re.search(patterns['doctor_title'], line, re.IGNORECASE)
            if doctor_match and not data.get('vendor_name'):
                full_doctor_line = line
                data['vendor_name'] = full_doctor_line.strip()

            
            license_match = re.search(patterns['license_number'], line, re.IGNORECASE)
            if license_match:
                data['vendor_tax_id'] = license_match.group(1)

            
            address_match = re.search(patterns['address'], line, re.IGNORECASE)
            if address_match and not data.get('vendor_address'):
                data['vendor_address'] = address_match.group(0).strip()

            
            if not data.get('vendor_address'):
                street_match = re.search(patterns['street_name'], line)
                if street_match:
                    data['vendor_address'] = street_match.group(0).strip()

        
        if not data.get('vendor_name'):
            for line in lines:
                if 'from' in line.lower() and len(line.strip()) > 5:
                    match = re.search(r'from\s+([^\\n]+)', line, re.IGNORECASE)
                    if match:
                        data['vendor_name'] = match.group(1).strip().title()
                        break

        
        for line in lines:
            if 'to' in line.lower() and len(line.strip()) > 3:
                match = re.search(r'to\s+([^\\n]+)', line, re.IGNORECASE)
                if match:
                    data['customer_name'] = match.group(1).strip().title()
                    break

        
        phone_matches = re.findall(patterns['phone'], text)
        if phone_matches:
            data['vendor_phone'] = phone_matches[0]

        
        email_matches = re.findall(patterns['email'], text, re.IGNORECASE)
        if email_matches:
            data['vendor_email'] = email_matches[0]

        
        for line in lines:
            if any(keyword in line.lower() for keyword in ['prescription', 'invoice', 'bill', 'receipt', 'form']):
                if not data.get('description'):
                    data['description'] = line.strip()
                break

        
        signatures = []
        for i, line in enumerate(lines):
            if 'signature' in line.lower():
                
                for j in range(i+1, min(i+4, len(lines))):
                    next_line = lines[j].strip()
                    if next_line and len(next_line) > 1 and not any(keyword in next_line.lower()
                        for keyword in ['date', 'prescriber', 'patient', '$', 'pharmacy']):
                        signatures.append(next_line)
                        break
        if signatures:
            data['signatures'] = ', '.join(signatures)

        
        items = []

        
        for line in lines:
            if '$' in line and len(line) > 3:
                if line.count('$') == 1:
                    parts = line.split('$')
                    if len(parts) == 2:
                        item_name = parts[0].strip()
                        amount_str = parts[1].strip().split()[0]
                        try:
                            amount = float(amount_str.replace(',', ''))
                            if item_name and len(item_name) > 1:
                                items.append({
                                    'name': item_name,
                                    'total': amount,
                                    'quantity': 1.0,
                                    'unit_price': amount
                                })
                        except:
                            pass

        
        item_indicators = ['R ', 'Service', 'Product', 'Item']
        for line in lines:
            line_clean = line.strip()
            
            if any(line_clean.startswith(indicator) for indicator in item_indicators):
                if len(line_clean) > 2 and not any(skip in line_clean.lower()
                    for skip in ['receipt', 'reference', 'return', 'refund']):
                    
                    line_index = lines.index(line_clean)
                    amount = 0.0

                    
                    for j in range(line_index + 1, min(line_index + 4, len(lines))):
                        if '$' in lines[j]:
                            amount_match = re.search(r'\$\s*(\d+(?:\.\d{2})?)', lines[j])
                            if amount_match:
                                try:
                                    amount = float(amount_match.group(1))
                                    break
                                except:
                                    pass

                    items.append({
                        'name': line_clean,
                        'total': amount,
                        'quantity': 1.0,
                        'unit_price': amount
                    })

        
        service_keywords = ['services rendered', 'consultation', 'treatment', 'therapy', 'examination']
        for line in lines:
            line_lower = line.lower().strip()
            if any(keyword in line_lower for keyword in service_keywords):
                if not any(item['name'].lower() == line_lower for item in items):
                    items.append({
                        'name': line.strip(),
                        'total': 0.0,
                        'quantity': 1.0,
                        'unit_price': 0.0
                    })

        
        unique_items = []
        seen_names = set()
        for item in items:
            name_clean = item['name'].lower().strip()
            if name_clean not in seen_names and len(name_clean) > 1:
                seen_names.add(name_clean)
                unique_items.append(item)

        data['items'] = unique_items[:3]  

        
        amount_matches = re.findall(patterns['amount'], text)
        if amount_matches:
            amounts = [float(amt.replace(',', '')) for amt in amount_matches]
            if amounts:
                data['total'] = max(amounts) if max(amounts) > 0 else 0.0

        return data

    def _extract_with_llm(self, extracted_text: str) -> Dict:
        """Extract data using LLM with highly focused and accurate prompts"""
        groq_llm = ChatGroq(
            groq_api_key=self.groq_api_key,
            model_name=self.text_model,
            temperature=0
        )

        messages = [
            {
                "role": "user",
                "content": (
                    "Extract information from this handwritten document text and return ONLY valid JSON.\n\n"
                    "Rules:\n"
                    "- Extract only clearly visible information\n"
                    "- Use empty strings for missing data\n"
                    "- For amounts, use numbers only (no currency symbols)\n"
                    "- Return valid JSON format only\n\n"
                    "Required JSON structure (include ALL fields even if empty):\n"
                    "{\n"
                    '  "invoice_number": "",\n'
                    '  "invoice_date": "",\n'
                    '  "due_date": "",\n'
                    '  "purchase_order_number": "",\n'
                    '  "reference_number": "",\n'
                    '  "vendor_name": "",\n'
                    '  "vendor_address": "",\n'
                    '  "vendor_phone": "",\n'
                    '  "vendor_email": "",\n'
                    '  "vendor_website": "",\n'
                    '  "vendor_tax_id": "",\n'
                    '  "customer_name": "",\n'
                    '  "customer_address": "",\n'
                    '  "customer_phone": "",\n'
                    '  "customer_email": "",\n'
                    '  "customer_tax_id": "",\n'
                    '  "bill_to_name": "",\n'
                    '  "bill_to_address": "",\n'
                    '  "ship_to_name": "",\n'
                    '  "ship_to_address": "",\n'
                    '  "currency": "USD",\n'
                    '  "subtotal": 0,\n'
                    '  "tax_type": "",\n'
                    '  "tax_rate": 0,\n'
                    '  "tax_amount": 0,\n'
                    '  "discount_amount": 0,\n'
                    '  "shipping_amount": 0,\n'
                    '  "handling_fee": 0,\n'
                    '  "total": 0,\n'
                    '  "amount_paid": 0,\n'
                    '  "balance_due": 0,\n'
                    '  "payment_terms": "",\n'
                    '  "payment_method": "",\n'
                    '  "payment_due_date": "",\n'
                    '  "project_name": "",\n'
                    '  "description": "",\n'
                    '  "notes": "",\n'
                    '  "terms_conditions": "",\n'
                    '  "item_1_name": "",\n'
                    '  "item_1_description": "",\n'
                    '  "item_1_category": "",\n'
                    '  "item_1_quantity": 1,\n'
                    '  "item_1_unit": "",\n'
                    '  "item_1_unit_price": 0,\n'
                    '  "item_1_total": 0,\n'
                    '  "item_2_name": "",\n'
                    '  "item_2_description": "",\n'
                    '  "item_2_category": "",\n'
                    '  "item_2_quantity": 1,\n'
                    '  "item_2_unit": "",\n'
                    '  "item_2_unit_price": 0,\n'
                    '  "item_2_total": 0,\n'
                    '  "item_3_name": "",\n'
                    '  "item_3_description": "",\n'
                    '  "item_3_category": "",\n'
                    '  "item_3_quantity": 1,\n'
                    '  "item_3_unit": "",\n'
                    '  "item_3_unit_price": 0,\n'
                    '  "item_3_total": 0,\n'
                    '  "signatures": "",\n'
                    '  "handwritten_notes": "",\n'
                    '  "stamps_seals": "",\n'
                    '  "other_information": ""\n'
                    "}\n\n"
                    f"Text to analyze:\n{extracted_text}"
                )
            }
        ]

        try:
            response = groq_llm.invoke(messages)
            response_text = response.content.strip()

            
            print(f"🔍 LLM Response length: {len(response_text)}")
            if len(response_text) < 100:
                print(f"🔍 LLM Response content: {response_text}")

            
            if not response_text:
                print("❌ LLM returned empty response")
                return {}

            
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.startswith('```'):
                response_text = response_text[3:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]

            response_text = response_text.strip()

            
            if not response_text:
                print("❌ LLM response is empty after cleaning")
                return {}

            
            try:
                parsed_data = json.loads(response_text)
                print("✅ LLM extraction successful")
                return parsed_data
            except json.JSONDecodeError as json_error:
                print(f"❌ JSON parsing error: {json_error}")
                print(f"🔍 Response text: {response_text[:200]}...")
                return {}

        except Exception as e:
            print(f"❌ LLM extraction error: {str(e)}")
            return {}

    def _merge_extraction_results(self, rule_data: Dict, llm_data: Dict) -> Dict:
        """Intelligently merge rule-based and LLM extraction results"""
        merged = {}

        
        default_model = InvoiceData()
        merged = default_model.model_dump()

        
        for key, value in llm_data.items():
            if value and str(value).strip() and str(value) != "0":
                if key == 'items' and isinstance(value, list):
                    merged['items'] = value
                else:
                    merged[key] = value

        
        for key, value in rule_data.items():
            if value and str(value).strip() and str(value) != "0":
                if key == 'items' and isinstance(value, list) and value:
                    
                    existing_items = merged.get('items', [])
                    rule_items = value

                    
                    all_items = []
                    seen_names = set()

                    
                    for item in rule_items:
                        name_clean = item.get('name', '').lower().strip()
                        if name_clean and name_clean not in seen_names:
                            all_items.append(item)
                            seen_names.add(name_clean)

                    
                    for item in existing_items:
                        name_clean = item.get('name', '').lower().strip()
                        if name_clean and name_clean not in seen_names:
                            all_items.append(item)
                            seen_names.add(name_clean)

                    merged['items'] = all_items[:3]  
                else:
                    merged[key] = value

        return merged

    def _convert_pydantic_to_flat_dict(self, invoice_model: InvoiceData) -> Dict:
        """Convert Pydantic model to flat dictionary for CSV compatibility"""
        data = invoice_model.model_dump()

        
        flat_data = {}
        for key, value in data.items():
            if key == 'items':
                
                for i, item in enumerate(value[:3], 1):  
                    flat_data[f'item_{i}_name'] = item.get('name', '')
                    flat_data[f'item_{i}_description'] = item.get('description', '')
                    flat_data[f'item_{i}_category'] = item.get('category', '')
                    flat_data[f'item_{i}_quantity'] = item.get('quantity', 0)
                    flat_data[f'item_{i}_unit'] = item.get('unit', '')
                    flat_data[f'item_{i}_unit_price'] = item.get('unit_price', 0)
                    flat_data[f'item_{i}_total'] = item.get('total', 0)

                
                for i in range(len(value) + 1, 4):
                    flat_data[f'item_{i}_name'] = ''
                    flat_data[f'item_{i}_description'] = ''
                    flat_data[f'item_{i}_category'] = ''
                    flat_data[f'item_{i}_quantity'] = 0
                    flat_data[f'item_{i}_unit'] = ''
                    flat_data[f'item_{i}_unit_price'] = 0
                    flat_data[f'item_{i}_total'] = 0
            else:
                flat_data[key] = value

        return flat_data

    def _get_default_invoice_structure(self) -> Dict:
        """Return default invoice structure using Pydantic model"""
        default_model = InvoiceData()
        return self._convert_pydantic_to_flat_dict(default_model)

    def _ensure_complete_data_capture(self, original_text: str, invoice_data: Dict) -> Dict:
        """Ensure all information is captured with minimal data in other_information"""
        
        lines = [line.strip() for line in original_text.split('\n') if line.strip()]

        
        extracted_info = set()
        for key, value in invoice_data.items():
            if key != 'other_information' and value and str(value).strip():
                if isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            for item_value in item.values():
                                if item_value and str(item_value).strip():
                                    extracted_info.add(str(item_value).lower().strip())
                        else:
                            extracted_info.add(str(item).lower().strip())
                else:
                    extracted_info.add(str(value).lower().strip())

        
        uncaptured_lines = []
        important_keywords = ['form', 'number', 'date', 'address', 'pharmacy', 'patient', 'card', 'stamp', 'supply']

        for line in lines:
            line_lower = line.lower().strip()
            
            if any(extracted.replace(' ', '') in line_lower.replace(' ', '') for extracted in extracted_info if len(extracted) > 2):
                continue

            
            if (any(keyword in line_lower for keyword in important_keywords) or
                re.search(r'\d+', line) or  
                len(line.split()) <= 5):    
                uncaptured_lines.append(line)

        
        if uncaptured_lines:
            invoice_data['other_information'] = ' | '.join(uncaptured_lines[:10])  
        else:
            invoice_data['other_information'] = ''

        return invoice_data

    def process_single_image(self, image_path: Path) -> Dict:
        """Process a single image and return results"""
        print(f"\n📄 Processing: {image_path.name}")
        
        try:
            
            image = Image.open(image_path)
            print(f"   📏 Original size: {image.size}")
            
            
            processed_image = self.preprocess_image(image)
            
            
            sections = self.split_image_into_sections(processed_image, sections=3, overlap=0.2)
            print(f"   ✂️  Split into {len(sections)} sections")
            
            
            text_sections = []
            for i, section in enumerate(sections, 1):
                print(f"   🔍 Processing section {i}/{len(sections)}...")
                section_text = self.extract_handwritten_text(section)
                text_sections.append(section_text)
            
            
            print("   🔗 Consolidating sections...")
            final_text = self.consolidate_text_sections(text_sections)

            
            print("   📊 Extracting invoice data...")
            invoice_data = self.extract_invoice_data(final_text)

            
            invoice_data = self._ensure_complete_data_capture(final_text, invoice_data)

            result = {
                "filename": image_path.name,
                "processed_at": datetime.now().isoformat(),
                "image_size": image.size,
                "sections_processed": len(sections),
                "extracted_text": final_text,
                "invoice_data": invoice_data,
                "status": "success"
            }
            
            print(f"   ✅ Successfully processed {image_path.name}")
            return result
            
        except Exception as e:
            error_result = {
                "filename": image_path.name,
                "processed_at": datetime.now().isoformat(),
                "error": str(e),
                "status": "error"
            }
            print(f"   ❌ Error processing {image_path.name}: {str(e)}")
            return error_result

    def process_all_images(self) -> List[Dict]:
        """Process all images in the input directory"""
        
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        
        image_files = [
            f for f in self.input_dir.iterdir() 
            if f.is_file() and f.suffix.lower() in supported_formats
        ]
        
        if not image_files:
            print(f"❌ No image files found in {self.input_dir}")
            print(f"   Supported formats: {', '.join(supported_formats)}")
            return []
        
        print(f"🔍 Found {len(image_files)} image(s) to process")
        
        results = []
        for image_file in image_files:
            result = self.process_single_image(image_file)
            results.append(result)
        
        return results

    def save_csv_results(self, results: List[Dict], timestamp: str):
        """Save invoice data to CSV format"""
        
        invoice_results = [
            result for result in results
            if result["status"] == "success" and result.get("invoice_data")
        ]

        if not invoice_results:
            print("   ⚠️  No invoice data to save to CSV")
            return

        csv_file = self.output_dir / f"invoice_data_{timestamp}.csv"

        
        fieldnames = list(invoice_results[0]["invoice_data"].keys())
        
        fieldnames = ["filename", "processed_at"] + fieldnames

        with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in invoice_results:
                
                row_data = {
                    "filename": result["filename"],
                    "processed_at": result["processed_at"]
                }
                row_data.update(result["invoice_data"])
                writer.writerow(row_data)

        print(f"   📊 CSV file: {csv_file}")
        return csv_file

    def save_results(self, results: List[Dict]):
        """Save processing results to files"""
        if not results:
            print("❌ No results to save")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        
        json_file = self.output_dir / f"ocr_results_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        
        csv_file = self.save_csv_results(results, timestamp)

        
        text_dir = self.output_dir / f"extracted_text_{timestamp}"
        text_dir.mkdir(exist_ok=True)

        for result in results:
            if result["status"] == "success" and result.get("extracted_text"):
                filename = Path(result["filename"]).stem
                text_file = text_dir / f"{filename}_extracted.txt"
                with open(text_file, 'w', encoding='utf-8') as f:
                    f.write(f"Extracted from: {result['filename']}\n")
                    f.write(f"Processed at: {result['processed_at']}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(result["extracted_text"])

        print(f"\n💾 Results saved:")
        print(f"   📄 JSON summary: {json_file}")
        if csv_file:
            print(f"   📊 CSV invoice data: {csv_file}")
        print(f"   📁 Text files: {text_dir}")

def main():
    """Main function to run the OCR tool"""
    print("🚀 Starting Handwritten OCR Tool")
    print("=" * 50)
    
    try:
        
        ocr_tool = HandwrittenOCR()
        
        
        results = ocr_tool.process_all_images()
        
        
        ocr_tool.save_results(results)
        
        
        successful = sum(1 for r in results if r["status"] == "success")
        failed = len(results) - successful
        
        print(f"\n📊 Processing Summary:")
        print(f"   ✅ Successful: {successful}")
        print(f"   ❌ Failed: {failed}")
        print(f"   📁 Total processed: {len(results)}")
        
        if successful > 0:
            print(f"\n🎉 OCR processing completed successfully!")
            print(f"   Check the 'output' folder for results:")
            print(f"   - JSON file with complete results")
            print(f"   - CSV file with structured invoice data")
            print(f"   - Individual text files with extracted content")
        
    except Exception as e:
        print(f"❌ Fatal error: {str(e)}")

if __name__ == "__main__":
    main()
