#!/usr/bin/env python3
"""
Startup Script for Automated Invoice Extractor

This script starts the unified FastAPI application with all components:
- AutoGen multi-agent orchestration
- Standard mode processing
- Handwritten mode processing  
- RAG chatbot
- Comprehensive logging
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path
import uvicorn
from dotenv import load_dotenv

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Load environment variables
load_dotenv()

from config import settings
from unified_logging import main_logger

def check_environment():
    """Check if all required environment variables are set"""
    required_vars = ["GROQ_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"ERROR: Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these variables in your .env file")
        return False

    return True

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import fastapi
        import uvicorn
        import groq
        import autogen
        import langchain_groq
        import chromadb
        import pandas
        import PIL
        import cv2
        import pytesseract
        import fitz  # PyMuPDF
        
        print("SUCCESS: All required dependencies are installed")
        return True

    except ImportError as e:
        print(f"ERROR: Missing dependency: {e}")
        print("Please install all dependencies: pip install -r requirements.txt")
        return False

def setup_directories():
    """Ensure all required directories exist"""
    try:
        settings.ensure_directories()
        print("SUCCESS: All required directories created/verified")
        return True
    except Exception as e:
        print(f"ERROR: Error creating directories: {e}")
        return False

def print_startup_info():
    """Print startup information"""
    print("\n" + "="*60)
    print("Automated Invoice Extractor")
    print("="*60)
    print(f"Application: {settings.app_name}")
    print(f"Version: {settings.app_version}")
    print(f"Host: {settings.host}:{settings.port}")
    print(f"Groq API Key: {settings.groq_api_key[:10]}..." if settings.groq_api_key else "ERROR: Not configured")
    print(f"Log Level: {settings.log_level}")
    print(f"Debug Mode: {settings.debug}")
    print(f"Auto Reload: {settings.reload}")
    print("\nDirectories:")
    print(f"   Input: {settings.input_dir}")
    print(f"   Output: {settings.output_dir}")
    print(f"   Logs: {settings.logs_dir}")
    print(f"   Vector Store: {settings.vector_store_path}")
    print("\nAutoGen Models:")
    print(f"   Analyzer: {settings.autogen_analyzer_model}")
    print(f"   Standard: {settings.autogen_standard_model}")
    print(f"   Handwritten: {settings.autogen_handwritten_model}")
    print(f"   RAG: {settings.autogen_rag_model}")
    print("\nFeatures:")
    print("   - AutoGen multi-agent orchestration")
    print("   - Intelligent document routing")
    print("   - Standard mode (PDFs, structured docs)")
    print("   - Handwritten mode (handwritten images)")
    print("   - RAG chatbot (document Q&A)")
    print("   - Unified CSV output")
    print("   - Comprehensive logging")
    print("\nAPI Endpoints:")
    print(f"   Main API: http://{settings.host}:{settings.port}")
    print(f"   Documentation: http://{settings.host}:{settings.port}/docs")
    print(f"   ReDoc: http://{settings.host}:{settings.port}/redoc")
    print(f"   Health Check: http://{settings.host}:{settings.port}/health")
    print(f"   Process Document: POST /process-document/csv")
    print(f"   Chatbot: POST /chatbot")
    print(f"   Refresh DB: DELETE /refresh-db")
    print("="*60)

def main():
    """Main startup function"""
    parser = argparse.ArgumentParser(description="Start the Automated Invoice Extractor")
    parser.add_argument("--host", default=settings.host, help="Host to bind to")
    parser.add_argument("--port", type=int, default=settings.port, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", default=settings.reload, help="Enable auto-reload")
    parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")
    parser.add_argument("--log-level", default=settings.log_level, 
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="Log level")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes")
    parser.add_argument("--check-only", action="store_true", help="Only check environment and exit")
    
    args = parser.parse_args()
    
    # Override settings with command line arguments
    if args.no_reload:
        reload = False
    else:
        reload = args.reload
    
    # Print startup information
    print_startup_info()
    
    # Check environment
    print("\nChecking environment...")
    if not check_environment():
        sys.exit(1)

    # Check dependencies
    print("\nChecking dependencies...")
    if not check_dependencies():
        sys.exit(1)

    # Setup directories
    print("\nSetting up directories...")
    if not setup_directories():
        sys.exit(1)

    # If check-only mode, exit here
    if args.check_only:
        print("\nSUCCESS: All checks passed! System is ready to start.")
        sys.exit(0)
    
    # Initialize logging
    main_logger.start_session("application_startup")
    main_logger.info("Starting Automated Invoice Extractor")
    
    try:
        print(f"\nStarting server on {args.host}:{args.port}")
        print("Press Ctrl+C to stop the server")
        print("\n" + "="*60 + "\n")

        # Start the server
        uvicorn.run(
            "main:app",
            host=args.host,
            port=args.port,
            reload=reload,
            log_level=args.log_level.lower(),
            workers=args.workers if not reload else 1,  # Workers only work without reload
            access_log=True,
            use_colors=True
        )

    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
        main_logger.info("Server stopped by user")
        main_logger.end_session(success=True, summary={"reason": "user_interrupt"})

    except Exception as e:
        print(f"\n\nERROR: Server failed to start: {e}")
        main_logger.error("Server startup failed", error_type="startup_error", context={"error": str(e)})
        main_logger.end_session(success=False, summary={"error": str(e)})
        sys.exit(1)

if __name__ == "__main__":
    main()
